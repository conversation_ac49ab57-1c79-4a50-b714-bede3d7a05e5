<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Website Test Page</h1>
        
        <div class="status success">
            ✅ HTML is loading correctly
        </div>
        
        <div class="status success">
            ✅ CSS styles are working
        </div>
        
        <div id="js-status" class="status error">
            ❌ JavaScript not loaded yet
        </div>
        
        <div id="backend-status" class="status error">
            ❌ Backend connection not tested
        </div>
        
        <button onclick="testBackend()">Test Backend Connection</button>
        <button onclick="testReactApp()">Open React App</button>
        
        <div id="debug-info">
            <h3>Debug Information:</h3>
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
            <p><strong>Screen Size:</strong> <span id="screen-size"></span></p>
        </div>
    </div>

    <script>
        // Test JavaScript
        document.getElementById('js-status').className = 'status success';
        document.getElementById('js-status').innerHTML = '✅ JavaScript is working';
        
        // Fill debug info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('screen-size').textContent = screen.width + 'x' + screen.height;
        
        // Test backend connection
        async function testBackend() {
            const statusDiv = document.getElementById('backend-status');
            statusDiv.innerHTML = '⏳ Testing backend connection...';
            statusDiv.className = 'status';
            
            try {
                const response = await fetch('http://localhost:5007/health');
                const data = await response.json();
                
                if (data.success) {
                    statusDiv.innerHTML = '✅ Backend connection successful';
                    statusDiv.className = 'status success';
                } else {
                    statusDiv.innerHTML = '❌ Backend returned error: ' + data.message;
                    statusDiv.className = 'status error';
                }
            } catch (error) {
                statusDiv.innerHTML = '❌ Backend connection failed: ' + error.message;
                statusDiv.className = 'status error';
            }
        }
        
        function testReactApp() {
            window.open('http://localhost:8081', '_blank');
        }
        
        // Auto-test backend on load
        setTimeout(testBackend, 1000);
    </script>
</body>
</html>
