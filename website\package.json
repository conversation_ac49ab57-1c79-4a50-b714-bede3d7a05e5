{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/react": "^3.13.0", "@chakra-ui/theme-tools": "^2.2.6", "@elevenlabs/react": "^0.3.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@react-three/postprocessing": "^2.15.11", "@tanstack/react-query": "^5.56.2", "@types/three": "^0.175.0", "aos": "^2.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "components": "^0.1.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "dotted-map": "^2.2.3", "elevenlabs": "^1.59.0", "embla-carousel-react": "^8.3.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "firebase": "^11.10.0", "framer-motion": "^10.18.0", "gsap": "^3.12.7", "helmet": "^8.1.0", "input-otp": "^1.2.4", "joi": "^17.13.3", "lucide-react": "^0.462.0", "maath": "^0.10.7", "morgan": "^1.10.0", "motion": "^12.19.0", "next": "^15.2.1", "next-themes": "^0.3.0", "nodemailer": "^7.0.3", "react": "^18.3.1", "react-countup": "^6.5.3", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.30.0", "react-spring": "^9.7.5", "react-tilt": "^1.0.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "styled": "^1.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.154.0", "three-stdlib": "^2.36.0", "vaul": "^0.9.3", "zod": "^3.23.8", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/aos": "^3.0.7", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}