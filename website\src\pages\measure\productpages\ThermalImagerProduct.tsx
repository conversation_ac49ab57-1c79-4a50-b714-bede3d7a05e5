import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Check,
  Download,
  Phone,
  Zap,
  Monitor,
  Database,
  Wifi,
  Battery,
  Thermometer,
  ChevronDown,
  Gauge,
  Shield,
  BarChart,
  Camera,
  Eye,
  ChevronRight
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';
import Carousel from '@/components/Carousel';

const ThermalImagerProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [featuresExpanded, setFeaturesExpanded] = useState(false);
  const [specsExpanded, setSpecsExpanded] = useState(false);
  const [brochureDropdownOpen, setBrochureDropdownOpen] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Product list for dropdown
  const productList = [
    { id: 'tc-s030', model: 'TC S030', subtitle: 'Entry Level Thermal Imager' },
    { id: 'tc-e050', model: 'TC E050', subtitle: 'Pocket Thermal Imager' },
    { id: 'ma-250', model: 'MA 250', subtitle: 'Mobile Thermal Imager' },
    { id: 'tc-2150', model: 'TC 2150', subtitle: 'Compact Thermal Imager' },
    { id: 'tc-2250', model: 'TC 2250', subtitle: 'Enhanced Compact Imager' },
    { id: 'tc-s240', model: 'TC S240', subtitle: 'Touch Screen Imager' },
    { id: 'tc-3151', model: 'TC 3151', subtitle: 'Professional Thermal Imager' },
    { id: 'tc-3250', model: 'TC 3250', subtitle: 'Manual Focus Imager' },
    { id: 'tc-3360', model: 'TC 3360', subtitle: 'Wide Field Imager' },
    { id: 'tc-p360', model: 'TC P360', subtitle: 'Pistol Grip Imager' },
    { id: 'tc-4360', model: 'TC 4360', subtitle: 'High Resolution Imager' },
    { id: 'tc-4460', model: 'TC 4460 / TC 4460H', subtitle: 'Extended Range Imager' },
    { id: 'tc-3660', model: 'TC 3660', subtitle: 'Advanced Thermal Imager' },
    { id: 'tc-4660', model: 'TC 4660 / TC 4660H', subtitle: 'High-End Thermal Imager' },
    { id: 'tcc-7460', model: 'TCC 7460 / TCC 742K', subtitle: 'Professional Camcorder' },
    { id: 'tcc-7660', model: 'TCC 7660 / TCC 762K', subtitle: 'High-Res Camcorder' },
    { id: 'tcc-812k', model: 'TCC 812K', subtitle: 'Ultra-High Res Camcorder' }
  ];

  // Complete product data with enhanced features and specs
  // Added brochure property for each product
  const productData = {
    'tc-s030': {
      id: 'tc-s030',
      model: 'TC S030',
      subtitle: 'Entry Level Thermal Imager',
      image: '/TC-S030-Overview.png',
      images: [
        '/TC-S030-Overview.png',
        '/TC-S030-Inside.png'
      ],
      irResolution: '240× 240',
      temperatureRange: '-20°C to 350°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Entry-level thermal imager with touch screen interface, perfect for basic thermal imaging applications with reliable performance and user-friendly operation.',
      keyFeatures: [
        'IR Resolution: 96 × 96 (9,216 pixels)',
        'SuperIR Resolution: Yes, on captured images & live view',
        'Temperature Range: -20°C to 350°C',
        'Accuracy: ±2°C, ±2%',
        'Field of View (FOV): 50° × 50°',
        'NETD: < 50 mK',
        'Focus: Fixed Focus & minimum focus of 0.1m',
        'Image Frequency: 25 Hz',
        'Touch screen interface',
        'WiFi connectivity',
        'Built-in memory storage',
        'IP54 protection rating',
        'Portable design',
        'Easy operation'
      ],
      technicalSpecs: {
        'IR Resolution': '96 × 96 (9,216 pixels)',
        'SuperIR Resolution': 'Yes, on captured images & live view',
        'Temperature Range': '-20°C to 350°C',
        'Accuracy': '±2°C, ±2%',
        'Field of View': '50° × 50°',
        'NETD': '< 50 mK',
        'Focus': 'Fixed Focus & minimum focus of 0.1m',
        'Image Frequency': '25 Hz',
        'Display': '320 × 240 Resolution, 3.5" LCD touch screen',
        'Visual Camera': '640 × 480 (0.3 MP)',
        'Battery': 'Li-ion, 4 hours operation (Built-in)',
        'Memory': 'Built-in 4GB',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54',
        'Interfaces': 'USB, WiFi',
        'Weight': 'Lightweight design',
        'Dimensions': 'Compact form factor',
        'Image Storage': 'Multiple file formats'
      },
      applications: [
        'Building inspection',
        'HVAC diagnostics',
        'Electrical maintenance',
        'Mechanical troubleshooting',
        'Energy audits',
        'Basic thermal analysis'
      ],
      advantages: [
        'User-friendly touch interface',
        'Compact and portable design',
        'Built-in WiFi connectivity',
        'SuperIR image enhancement',
        'Cost-effective solution',
        'Reliable performance'
      ],
      brochure: '/TCS030.pdf'
    },
    'tc-e050': {
      id: 'tc-e050',
      model: 'TC E050',
      subtitle: 'Pocket Thermal Imager',
      image: '/TCE050-overview.png',
      images: [
        '/TCE050-overview.png',
        '/TCE050-inside.png'
      ],
      irResolution: '96 × 96',
      temperatureRange: '-20°C to 550°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Pocket-sized thermal imager with extended battery life, designed for mobile professionals who need reliable thermal imaging in a compact form factor.',
      keyFeatures: [
        'IR Resolution: 96 × 96 (9,216 pixels)',
        'SuperIR Resolution: 240 × 240',
        'Temperature Range: -20°C to 550°C',
        'Accuracy: ±2°C, ±2%',
        'Field of View (FOV): 50° × 50°',
        'NETD: < 50 mK',
        'Focus: Fixed & minimum distance of 0.1m',
        'Image Frequency: 25 Hz',
        'Extended battery life (8 hours)',
        'Laser pointer included',
        'Ultra-portable design',
        'Professional measurement accuracy',
        'Rugged construction',
        'Easy-to-use interface'
      ],
      technicalSpecs: {
        'IR Resolution': '96 × 96 (9,216 pixels)',
        'SuperIR Resolution': '240 × 240',
        'Temperature Range': '-20°C to 550°C',
        'Accuracy': '±2°C, ±2%',
        'Field of View': '50° × 50°',
        'NETD': '< 50 mK',
        'Focus': 'Fixed & minimum distance of 0.1m',
        'Image Frequency': '25 Hz',
        'Display': '240 × 320 Resolution, 2.4" LCD Screen',
        'Visual Camera': '640 × 480 (0.3 MP)',
        'Battery': 'Li-ion, 8 hours operation (Built-in)',
        'Memory': 'Built-in 4GB flash memory',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54',
        'Interfaces': 'USB, Laser pointer',
        'Weight': 'Ultra-lightweight',
        'Dimensions': 'Pocket-sized',
        'Measurement Tools': 'Point and area measurement'
      },
      applications: [
        'Field service applications',
        'Electrical inspections',
        'Mechanical maintenance',
        'Building diagnostics',
        'Process monitoring',
        'Quality control'
      ],
      advantages: [
        'Ultra-portable design',
        'Extended 8-hour battery life',
        'SuperIR enhancement',
        'Laser pointer included',
        'Rugged construction',
        'Easy operation'
      ],
      brochure: '/TCE050.pdf'
    },
    'ma-250': {
      id: 'ma-250',
      model: 'MA 250',
      subtitle: 'Mobile Thermal Imager',
      image: '/MA250.jpeg',
      images: [
        '/MA250.jpeg'      ],
      irResolution: '256 × 192',
      temperatureRange: '-20°C to 400°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Smartphone attachment thermal imager for mobile applications, combining the power of thermal imaging with the convenience of your smartphone.',
      keyFeatures: [
        'IR Resolution: 256 × 192 (49,152 pixels)',
        'SuperIR Resolution: Yes, on captured images & live view',
        'Temperature Range: -20°C to 400°C',
        'Accuracy: ±2°C, ±2%',
        'Field of View (FOV): 50° × 37.2°',
        'NETD: < 40 mK',
        'Focus: Fixed & minimum distance 0.2m',
        'Image Frequency: 50 Hz',
        'Smartphone integration',
        'Universal compatibility',
        'Instant sharing capabilities',
        'Cloud storage access',
        'Mobile app control',
        'Cost-effective solution'
      ],
      technicalSpecs: {
        'IR Resolution': '256 × 192 (49,152 pixels)',
        'SuperIR Resolution': 'Yes, on captured images & live view',
        'Temperature Range': '-20°C to 400°C',
        'Accuracy': '±2°C, ±2%',
        'Field of View': '50° × 37.2°',
        'NETD': '< 40 mK',
        'Focus': 'Fixed & minimum distance 0.2m',
        'Image Frequency': '50 Hz',
        'Display': 'Uses smartphone display',
        'Communication': 'USB-C Android, USB-C iOS, Lightning iOS',
        'Power': 'Supplied by smartphone',
        'Storage': 'Uses smartphone storage',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54',
        'Compatibility': 'Android and iOS devices',
        'App Features': 'Advanced measurement tools',
        'Data Export': 'Multiple formats supported'
      },
      applications: [
        'Mobile inspections',
        'Field service work',
        'Real estate inspections',
        'Home energy audits',
        'Educational applications',
        'Quick thermal checks'
      ],
      advantages: [
        'Smartphone integration',
        'Instant sharing capabilities',
        'Cost-effective solution',
        'High portability',
        'Easy data management',
        'Universal compatibility'
      ],
      brochure: '/TH250.pdf'
    },
    'tc-s240': {
      id: 'tc-s240',
      model: 'TC S240',
      subtitle: 'Touch Screen Imager',
      image: '/TCS240-overview.png',
      images: [
        '/TCS240-overview.png',
        '/TCS240-inside.png'
      ],
      irResolution: '256 × 192',
      temperatureRange: '-20°C to 400°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Compact thermal imager with auto-rotation display and advanced touch screen interface for intuitive operation.',
      keyFeatures: [
        'IR Resolution: 256 × 192 (49,152 pixels)',
        'SuperIR Resolution: Yes, on captured images & live view',
        'Temperature Range: -20°C to 400°C',
        'Accuracy: ±2°C, ±2%',
        'Field of View (FOV): 50° × 37.2°',
        'NETD: < 40 mK',
        'Focus: Fixed & minimum distance 0.3m',
        'Image Frequency: 25 Hz',
        'Auto-rotation display',
        'High-resolution visual camera',
        'Multiple connectivity options',
        'Professional reliability',
        'Extended memory storage',
        'Advanced measurement tools'
      ],
      technicalSpecs: {
        'IR Resolution': '256 × 192 (49,152 pixels)',
        'SuperIR Resolution': 'Yes, on captured images & live view',
        'Temperature Range': '-20°C to 400°C',
        'Accuracy': '±2°C, ±2%',
        'Field of View': '50° × 37.2°',
        'NETD': '< 40 mK',
        'Focus': 'Fixed & minimum distance 0.3m',
        'Image Frequency': '25 Hz',
        'Display': '640 × 480 Resolution, 3.5" LCD touch screen with auto-rotation',
        'Visual Camera': '3264 × 2448 (8 MP)',
        'Battery': 'Li-ion, 4 hours operation (Built-in)',
        'Memory': 'Built-in 16 GB',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54',
        'Interfaces': 'USB, WiFi, Bluetooth',
        'Image Formats': 'Multiple format support',
        'Measurement Modes': 'Point, line, area, delta T'
      },
      applications: [
        'Professional inspections',
        'Building diagnostics',
        'Electrical maintenance',
        'Industrial applications',
        'Energy audits',
        'Quality assurance'
      ],
      advantages: [
        'Auto-rotation display',
        'High-resolution visual camera',
        'Touch screen interface',
        'Multiple connectivity options',
        'SuperIR enhancement',
        'Professional reliability'
      ],
      brochure: '/TCS240.pdf'
    },
    'tc-3151': {
      id: 'tc-3151',
      model: 'TC 3151',
      subtitle: 'Professional Thermal Imager',
      image: '/TCC-3150-overview.png',
      images: [
        '/TCC-3151-overview.png',
        '/TCC-3151-inside.png'
      ],
      irResolution: '192 × 144',
      temperatureRange: '-20°C to 550°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Professional thermal imager with micro SD storage and advanced measurement capabilities for demanding applications.',
      keyFeatures: [
        'IR Resolution: 192 × 144 (27,648 pixels)',
        'SuperIR Resolution: 384 × 288',
        'Temperature Range: -20°C to 550°C',
        'Accuracy: ±2°C, ±2%',
        'Field of View (FOV): 37.2° × 27.8°',
        'NETD: < 40 mK',
        'Focus: Fixed & minimum distance 0.5m',
        'Image Frequency: 25 Hz',
        'Interchangeable battery system',
        'Professional durability (2m drop test)',
        'Comprehensive connectivity',
        'Advanced measurement tools',
        'High-resolution visual camera',
        'Professional reporting features'
      ],
      technicalSpecs: {
        'IR Resolution': '192 × 144 (27,648 pixels)',
        'SuperIR Resolution': '384 × 288',
        'Temperature Range': '-20°C to 550°C',
        'Accuracy': '±2°C, ±2%',
        'Field of View': '37.2° × 27.8°',
        'NETD': '< 40 mK',
        'Focus': 'Fixed & minimum distance 0.5m',
        'Image Frequency': '25 Hz',
        'Display': '640 × 480 Resolution, 3.5" LCD touch screen',
        'Visual Camera': '3264 × 2448 (8 MP)',
        'Battery': 'Li-ion, 6 hours operation (Interchangeable Battery)',
        'Memory': '32 GB Micro SD card',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54, 2m drop test',
        'Interfaces': 'USB, WiFi, Bluetooth, Laser',
        'Analysis Software': 'Professional PC software',
        'File Formats': 'Multiple export options'
      },
      applications: [
        'Industrial maintenance',
        'Professional inspections',
        'Research applications',
        'Quality control',
        'Predictive maintenance',
        'Advanced diagnostics'
      ],
      advantages: [
        'Interchangeable battery',
        'Micro SD storage',
        'Professional durability',
        'Advanced measurement tools',
        'High-resolution visual camera',
        'Comprehensive connectivity'
      ],
      brochure: '/TC3151.pdf'
    },
    'tc-3250': {
      id: 'tc-3250',
      model: 'TC 3250',
      subtitle: 'Manual Focus Imager',
      image: '/thermal-measurement/TC-3250.png',
      images: [
        '/thermal-measurement/TC-3250.png',
        '/thermal-measurement/TC-3250.png'
      ],
      irResolution: '256 × 192',
      temperatureRange: '-20°C to 550°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Professional thermal imager with manual focus capability, providing precise thermal imaging for detailed analysis.',
      keyFeatures: [
        'IR Resolution: 256 × 192 (49,152 pixels)',
        'SuperIR Resolution: 512 × 384',
        'Temperature Range: -20°C to 550°C',
        'Accuracy: ±2°C, ±2%',
        'Field of View (FOV): 25° × 18.8°',
        'NETD: < 40 mK',
        'Focus: Manual & minimum distance 0.1m',
        'Image Frequency: 25 Hz',
        'Manual focus capability',
        'High SuperIR resolution',
        'Professional accuracy',
        'Rugged construction',
        'Advanced connectivity',
        'Precise measurements'
      ],
      technicalSpecs: {
        'IR Resolution': '256 × 192 (49,152 pixels)',
        'SuperIR Resolution': '512 × 384',
        'Temperature Range': '-20°C to 550°C',
        'Accuracy': '±2°C, ±2%',
        'Field of View': '25° × 18.8°',
        'NETD': '< 40 mK',
        'Focus': 'Manual & minimum distance 0.1m',
        'Image Frequency': '25 Hz',
        'Display': '640 × 480 Resolution, 3.5" LCD touch screen',
        'Visual Camera': '3264 × 2448 (8 MP)',
        'Battery': 'Li-ion, 6 hours operation (Interchangeable Battery)',
        'Memory': '32 GB Micro SD card',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54, 2m drop test',
        'Interfaces': 'USB, WiFi, Bluetooth, Laser',
        'Focus Range': 'Close-up to infinity',
        'Lens System': 'Professional optical design'
      },
      applications: [
        'Precision measurements',
        'Detailed inspections',
        'Research and development',
        'Quality control',
        'Scientific applications',
        'Professional diagnostics'
      ],
      advantages: [
        'Manual focus capability',
        'High SuperIR resolution',
        'Professional accuracy',
        'Rugged construction',
        'Advanced connectivity',
        'Precise measurements'
      ],
      brochure: '/brochures/TC-3250.pdf'
    },
    'tc-3360': {
      id: 'tc-3360',
      model: 'TC 3360',
      subtitle: 'Wide Field Imager',
      image: '/TC-3360-overview.png',
      images: [
        '/TC-3360-overview.png',
        '/TC-3360-inside.png'
      ],
      irResolution: '384 × 288',
      temperatureRange: '-20°C to 550°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Wide field thermal imager with enhanced resolution, ideal for comprehensive thermal analysis and large area inspections.',
      keyFeatures: [
        'IR Resolution: 384 × 288 (110,592 pixels)',
        'SuperIR Resolution: 768 × 576',
        'Temperature Range: -20°C to 550°C',
        'Accuracy: ±2°C, ±2%',
        'Field of View (FOV): Wide angle coverage',
        'NETD: < 40 mK',
        'Focus: Manual & minimum distance 0.1m',
        'Image Frequency: 25 Hz',
        'High resolution imaging',
        'Wide field of view',
        'Professional features',
        'Durable construction',
        'Advanced analysis tools',
        'Comprehensive documentation'
      ],
      technicalSpecs: {
        'IR Resolution': '384 × 288 (110,592 pixels)',
        'SuperIR Resolution': '768 × 576',
        'Temperature Range': '-20°C to 550°C',
        'Accuracy': '±2°C, ±2%',
        'Field of View': 'Wide angle coverage',
        'NETD': '< 40 mK',
        'Focus': 'Manual & minimum distance 0.1m',
        'Image Frequency': '25 Hz',
        'Display': '640 × 480 Resolution, 3.5" LCD touch screen',
        'Visual Camera': '3264 × 2448 (8 MP)',
        'Battery': 'Li-ion, 6 hours operation (Interchangeable Battery)',
        'Memory': '32 GB Micro SD card',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54, 2m drop test',
        'Interfaces': 'USB, WiFi, Bluetooth, Laser',
        'Optical Design': 'Wide-angle lens system',
        'Measurement Tools': 'Advanced analysis functions'
      },
      applications: [
        'Large area inspections',
        'Building surveys',
        'Industrial monitoring',
        'Solar panel inspections',
        'Electrical distribution',
        'Facility management'
      ],
      advantages: [
        'High resolution imaging',
        'Wide field of view',
        'Professional features',
        'Durable construction',
        'Advanced analysis tools',
        'Comprehensive documentation'
      ],
      brochure: '/TC-3360.pdf'
    },
    'tc-2150': {
      id: 'tc-2150',
      model: 'TC 2150',
      subtitle: 'Compact Thermal Imager',
      image: '/TC2150-overview.png',
      images: [
        '/TC2150-overview.png',
        '/TC2150-inside.png'
      ],
      irResolution: '192 × 144',
      temperatureRange: '-20°C to 550°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Compact thermal imager with WiFi connectivity, offering excellent thermal imaging capabilities in a portable design.',
      keyFeatures: [
        'IR Resolution: 192 × 144 (27,648 pixels)',
        'SuperIR Resolution: Yes, on captured images & live view',
        'Temperature Range: -20°C to 550°C',
        'Accuracy: ±2°C, ±2%',
        'Field of View (FOV): 27.8° × 37.2°',
        'NETD: < 40 mK',
        'Focus: Fixed & minimum distance 0.3m',
        'Image Frequency: 25 Hz',
        'Image Modes: Thermal/Visual/Fusion/PIP',
        'Visual Camera: 1600 x 1200 (2 MP)',
        'Display: 240 x 320 Resolution, 3.2" LCD screen',
        'Battery Backup: Approximately 6 hours (Built-in)',
        'Memory: Built-in 16 GB (90,000 Images)',
        'WiFi connectivity',
        'Compact and lightweight design'
      ],
      technicalSpecs: {
        'IR Resolution': '192 × 144 (27,648 pixels)',
        'SuperIR Resolution': 'Yes, on captured images & live view',
        'Temperature Range': '-20°C to 550°C',
        'Accuracy': '±2°C, ±2%',
        'Field of View': '27.8° × 37.2°',
        'NETD': '< 40 mK',
        'Focus': 'Fixed & minimum distance 0.3m',
        'Image Frequency': '25 Hz',
        'Image Modes': 'Thermal/Visual/Fusion/PIP',
        'Visual Camera': '1600 x 1200 (2 MP)',
        'Display': '240 x 320 Resolution, 3.2" LCD screen',
        'Battery Backup': 'Approximately 6 hours (Built-in)',
        'Memory': 'Built-in 16 GB (90,000 Images)',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54',
        'Interfaces': 'USB, WiFi',
        'Weight': 'Lightweight design',
        'Dimensions': 'Compact form factor'
      },
      applications: [
        'Building inspections',
        'Electrical maintenance',
        'HVAC diagnostics',
        'Mechanical troubleshooting',
        'Energy audits',
        'Preventive maintenance'
      ],
      advantages: [
        'Compact and portable design',
        'WiFi connectivity',
        'Multiple image modes',
        'Long battery life',
        'Large built-in memory',
        'High-quality visual camera'
      ],
      brochure: '/TC2150.pdf'
    },
    'tc-2250': {
      id: 'tc-2250',
      model: 'TC 2250',
      subtitle: 'Enhanced Compact Imager',
      image: '/thermal-measurement/TC-2250.png',
      images: [
        '/thermal-measurement/TC-2250.png',
        '/TC2250-inside.png'
      ],
      irResolution: '256 × 192',
      temperatureRange: '-20°C to 550°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Enhanced compact thermal imager with higher resolution and WiFi connectivity, providing professional-grade thermal imaging in a portable package.',
      keyFeatures: [
        'IR Resolution: 256 × 192 (49,152 pixels)',
        'SuperIR Resolution: Yes, on captured images & live view',
        'Temperature Range: -20°C to 550°C',
        'Accuracy: ±2°C, ±2%',
        'Field of View (FOV): 37.2° × 50.0°',
        'NETD: < 40 mK',
        'Focus: Fixed & minimum distance 0.3m',
        'Image Frequency: 25 Hz',
        'Image Modes: Thermal/Visual/Fusion/PIP',
        'Visual Camera: 1600 x 1200 (2 MP)',
        'Display: 480 x 640 Resolution, 3.2" LCD screen',
        'Battery Backup: Approximately 6 hours (Built-in)',
        'Memory: Built-in 16 GB (35,000 Images)',
        'WiFi connectivity',
        'Enhanced resolution for detailed imaging'
      ],
      technicalSpecs: {
        'IR Resolution': '256 × 192 (49,152 pixels)',
        'SuperIR Resolution': 'Yes, on captured images & live view',
        'Temperature Range': '-20°C to 550°C',
        'Accuracy': '±2°C, ±2%',
        'Field of View': '37.2° × 50.0°',
        'NETD': '< 40 mK',
        'Focus': 'Fixed & minimum distance 0.3m',
        'Image Frequency': '25 Hz',
        'Image Modes': 'Thermal/Visual/Fusion/PIP',
        'Visual Camera': '1600 x 1200 (2 MP)',
        'Display': '480 x 640 Resolution, 3.2" LCD screen',
        'Battery Backup': 'Approximately 6 hours (Built-in)',
        'Memory': 'Built-in 16 GB (35,000 Images)',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54',
        'Interfaces': 'USB, WiFi',
        'Weight': 'Lightweight design',
        'Dimensions': 'Compact form factor'
      },
      applications: [
        'Professional inspections',
        'Building diagnostics',
        'Electrical maintenance',
        'HVAC troubleshooting',
        'Energy audits',
        'Preventive maintenance'
      ],
      advantages: [
        'Higher resolution imaging',
        'WiFi connectivity',
        'Multiple image modes',
        'Long battery life',
        'Large built-in memory',
        'Enhanced display resolution'
      ],
      brochure: '/TC-2250.pdf'
    },
    'tc-p360': {
      id: 'tc-p360',
      model: 'TC P360',
      subtitle: 'Pistol Grip Imager',
      image: '/TCP-360-overview.png',
      images: [
        '/TCP-360-overview.png',
        '/TCP-360-inside.png'
      ],
      irResolution: '384 × 288',
      temperatureRange: '-20°C to 550°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Pistol grip thermal imager designed for comfortable operation during extended use, featuring high resolution and ergonomic design.',
      keyFeatures: [
        'IR Resolution: 384 × 288 (110,592 pixels)',
        'SuperIR Resolution: 768 × 576',
        'Temperature Range: -20°C to 550°C',
        'Accuracy: ±2°C, ±2%',
        'Ergonomic pistol grip design',
        'NETD: < 40 mK',
        'Focus: Manual & minimum distance 0.1m',
        'Image Frequency: 25 Hz',
        'Comfortable grip for extended use',
        'High resolution thermal imaging',
        'Professional durability',
        'User-friendly interface',
        'Extended operation capability',
        'Professional measurement tools'
      ],
      technicalSpecs: {
        'IR Resolution': '384 × 288 (110,592 pixels)',
        'SuperIR Resolution': '768 × 576',
        'Temperature Range': '-20°C to 550°C',
        'Accuracy': '±2°C, ±2%',
        'Design': 'Ergonomic pistol grip',
        'NETD': '< 40 mK',
        'Focus': 'Manual & minimum distance 0.1m',
        'Image Frequency': '25 Hz',
        'Display': '640 × 480 Resolution, 3.5" LCD touch screen',
        'Visual Camera': '3264 × 2448 (8 MP)',
        'Battery': 'Li-ion, 6 hours operation (Interchangeable Battery)',
        'Memory': '32 GB Micro SD card',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54, 2m drop test',
        'Interfaces': 'USB, WiFi, Bluetooth, Laser',
        'Ergonomics': 'Optimized for extended use',
        'Weight Distribution': 'Balanced design'
      },
      applications: [
        'Extended inspections',
        'Industrial maintenance',
        'Electrical troubleshooting',
        'Mechanical diagnostics',
        'Building inspections',
        'Field service work'
      ],
      advantages: [
        'Ergonomic design',
        'Comfortable grip',
        'High resolution',
        'Professional durability',
        'Extended operation',
        'User-friendly interface'
      ],
      brochure: '/TC-P360.pdf'
    },
    'tc-4360': {
      id: 'tc-4360',
      model: 'TC 4360',
      subtitle: 'High Resolution Imager',
      image: '/TC4360-overview.png',
      images: [
        '/TC4360-overview.png',
        '/TC4360-inside.png'
      ],
      irResolution: '384 × 288',
      temperatureRange: '-20°C to 650°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'High resolution thermal imager with advanced features for professional applications requiring superior image quality.',
      keyFeatures: [
        'IR Resolution: 384 × 288 (110,592 pixels)',
        'SuperIR Resolution: 768 × 576',
        'Temperature Range: -20°C to 650°C',
        'Accuracy: ±2°C, ±2%',
        'Advanced measurement tools',
        'NETD: < 30 mK',
        'Focus: Manual & minimum distance 0.1m',
        'Image Frequency: 50 Hz',
        'High thermal sensitivity',
        'Professional accuracy',
        'Extended battery life',
        'Rugged construction',
        'Comprehensive connectivity',
        'Advanced analysis capabilities'
      ],
      technicalSpecs: {
        'IR Resolution': '384 × 288 (110,592 pixels)',
        'SuperIR Resolution': '768 × 576',
        'Temperature Range': '-20°C to 650°C',
        'Accuracy': '±2°C, ±2%',
        'NETD': '< 30 mK',
        'Focus': 'Manual & minimum distance 0.1m',
        'Image Frequency': '50 Hz',
        'Display': '640 × 480 Resolution, 4" LCD touch screen',
        'Visual Camera': '5 MP with LED flash',
        'Battery': 'Li-ion, 8 hours operation (Interchangeable Battery)',
        'Memory': '64 GB internal + SD card',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54, 2m drop test',
        'Interfaces': 'USB, WiFi, Bluetooth, Laser',
        'Display Size': 'Large 4" screen',
        'Memory Expansion': 'SD card slot'
      },
      applications: [
        'Professional inspections',
        'Industrial diagnostics',
        'Research applications',
        'Quality assurance',
        'Predictive maintenance',
        'Advanced thermal analysis'
      ],
      advantages: [
        'High thermal sensitivity',
        'Professional accuracy',
        'Advanced measurement tools',
        'Extended battery life',
        'Rugged construction',
        'Comprehensive connectivity'
      ],
      brochure: '/TC-4360.pdf'
    },
    'tc-4460': {
      id: 'tc-4460',
      model: 'TC 4460 / TC 4460H',
      subtitle: 'Extended Range Imager',
      image: '/TC4460-overview.png',
      images: [
        '/TC4460-overview.png',
        '/TC4460-insde-01.png',
        '/TC4460-h-insde-02.png'
      ],
      irResolution: '480 × 360',
      temperatureRange: '-20°C to 1200°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Extended range thermal imager with superior resolution and temperature range for demanding industrial applications.',
      keyFeatures: [
        'IR Resolution: 480 × 360 (172,800 pixels)',
        'SuperIR Resolution: 960 × 720',
        'Temperature Range: -20°C to 1200°C',
        'Accuracy: ±2°C, ±2%',
        'Extended temperature range',
        'NETD: < 30 mK',
        'Focus: Manual & minimum distance 0.1m',
        'Image Frequency: 50 Hz',
        'High-temperature capability',
        'Superior thermal sensitivity',
        'Industrial-grade reliability',
        'Advanced analysis capabilities',
        'Professional durability',
        'Extended measurement range'
      ],
      technicalSpecs: {
        'IR Resolution': '480 × 360 (172,800 pixels)',
        'SuperIR Resolution': '960 × 720',
        'Temperature Range': '-20°C to 1200°C',
        'Accuracy': '±2°C, ±2%',
        'NETD': '< 30 mK',
        'Focus': 'Manual & minimum distance 0.1m',
        'Image Frequency': '50 Hz',
        'Display': '640 × 480 Resolution, 4" LCD touch screen',
        'Visual Camera': '5 MP with LED flash',
        'Battery': 'Li-ion, 8 hours operation (Interchangeable Battery)',
        'Memory': '64 GB internal + SD card',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54, 2m drop test',
        'Interfaces': 'USB, WiFi, Bluetooth, Laser',
        'High Temperature Mode': 'Up to 1200°C capability',
        'Measurement Precision': 'Enhanced for high temperatures'
      },
      applications: [
        'High-temperature processes',
        'Industrial furnaces',
        'Metal processing',
        'Glass manufacturing',
        'Petrochemical industry',
        'Power generation'
      ],
      advantages: [
        'Extended temperature range',
        'High resolution imaging',
        'Professional durability',
        'Advanced analysis capabilities',
        'Superior thermal sensitivity',
        'Industrial-grade reliability'
      ],
      brochures: [
        { name: 'TC-4460 Brochure', url: '/TC-4460.pdf' },
        { name: 'TC-4460H Brochure', url: '/TC-4460H.pdf' }
      ]
    },
    'tc-3660': {
      id: 'tc-3660',
      model: 'TC 3660',
      subtitle: 'Advanced Thermal Imager',
      image: '/Overview-image-3660.png',
      images: [
        '/Overview-image-3660.png',
        '/TC3660-inside.png'
      ],
      irResolution: '640 × 480',
      temperatureRange: '-20°C to 650°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Advanced thermal imager with ultra-high resolution for the most demanding professional applications.',
      keyFeatures: [
        'IR Resolution: 640 × 480 (307,200 pixels)',
        'SuperIR Resolution: 1280 × 960',
        'Temperature Range: -20°C to 650°C',
        'Accuracy: ±2°C, ±2%',
        'Ultra-high resolution',
        'NETD: < 30 mK',
        'Focus: Manual & minimum distance 0.1m',
        'Image Frequency: 50 Hz',
        'Superior image quality',
        'Professional accuracy',
        'Advanced features',
        'Exceptional detail',
        'Research-grade performance',
        'Premium thermal imaging'
      ],
      technicalSpecs: {
        'IR Resolution': '640 × 480 (307,200 pixels)',
        'SuperIR Resolution': '1280 × 960',
        'Temperature Range': '-20°C to 650°C',
        'Accuracy': '±2°C, ±2%',
        'NETD': '< 30 mK',
        'Focus': 'Manual & minimum distance 0.1m',
        'Image Frequency': '50 Hz',
        'Display': '640 × 480 Resolution, 4" LCD touch screen',
        'Visual Camera': '5 MP with LED flash',
        'Battery': 'Li-ion, 8 hours operation (Interchangeable Battery)',
        'Memory': '64 GB internal + SD card',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54, 2m drop test',
        'Interfaces': 'USB, WiFi, Bluetooth, Laser',
        'Image Quality': 'Ultra-high resolution',
        'Professional Features': 'Advanced measurement suite'
      },
      applications: [
        'Professional R&D',
        'Advanced diagnostics',
        'Precision measurements',
        'Quality control',
        'Scientific research',
        'High-end inspections'
      ],
      advantages: [
        'Ultra-high resolution',
        'Superior image quality',
        'Professional accuracy',
        'Advanced features',
        'Exceptional detail',
        'Research-grade performance'
      ],
      brochure: '/Krykard-TC-3660.pdf'
    },
    'tc-4660': {
      id: 'tc-4660',
      model: 'TC 4660 / TC 4660H',
      subtitle: 'High-End Thermal Imager',
      image: '/TC-4660-overview.png',
      images: [
        '/TC-4660-overview.png',
        '/TC-4660h-inside-01.png',
        '/TC-4660-inside-02.png'
      ],
      irResolution: '640 × 480',
      temperatureRange: '-20°C to 1200°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'High-end thermal imager with maximum resolution and extended temperature range for the most demanding professional applications.',
      keyFeatures: [
        'IR Resolution: 640 × 480 (307,200 pixels)',
        'SuperIR Resolution: 1280 × 960',
        'Temperature Range: -20°C to 1200°C',
        'Accuracy: ±2°C, ±2%',
        'Maximum performance',
        'NETD: < 30 mK',
        'Focus: Manual & minimum distance 0.1m',
        'Image Frequency: 50 Hz',
        'Maximum resolution capability',
        'Extended temperature range',
        'Professional-grade accuracy',
        'Advanced measurement tools',
        'Superior performance',
        'Industry-leading features'
      ],
      technicalSpecs: {
        'IR Resolution': '640 × 480 (307,200 pixels)',
        'SuperIR Resolution': '1280 × 960',
        'Temperature Range': '-20°C to 1200°C',
        'Accuracy': '±2°C, ±2%',
        'NETD': '< 30 mK',
        'Focus': 'Manual & minimum distance 0.1m',
        'Image Frequency': '50 Hz',
        'Display': '640 × 480 Resolution, 4" LCD touch screen',
        'Visual Camera': '5 MP with LED flash',
        'Battery': 'Li-ion, 8 hours operation (Interchangeable Battery)',
        'Memory': '64 GB internal + SD card',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54, 2m drop test',
        'Interfaces': 'USB, WiFi, Bluetooth, Laser',
        'Performance Level': 'Maximum capability',
        'Range': 'Extended high-temperature',
      },
      applications: [
        'High-temperature industrial processes',
        'Advanced research',
        'Precision diagnostics',
        'Quality control',
        'Scientific applications',
        'Professional inspections'
      ],
      advantages: [
        'Maximum resolution',
        'Extended temperature range',
        'Professional-grade accuracy',
        'Advanced measurement tools',
        'Superior performance',
        'Industry-leading features'
      ],
      brochures: [
        { name: 'TC-4660 Brochure', url: '/TC-4660.pdf' },
        { name: 'TC-4660H Brochure', url: '/TC-4660H.pdf' }
      ]
    },
    'tcc-7460': {
      id: 'tcc-7460',
      model: 'TCC 7460 / TCC 742K',
      subtitle: 'Professional Camcorder',
      image: '/TC-7460-overview.png',
      images: [
        '/TC-7460-overview.png',
        '/TC-742k-inside-01.png',
        '/TC-7460-inside-02.png'
      ],
      irResolution: '480 × 360',
      temperatureRange: '-20°C to 1200°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Professional thermal camcorder with video recording capabilities for comprehensive thermal documentation and analysis.',
      keyFeatures: [
        'IR Resolution: 480 × 360 (172,800 pixels)',
        'Video recording capability',
        'Temperature Range: -20°C to 1200°C',
        'Accuracy: ±2°C, ±2%',
        'Professional camcorder design',
        'NETD: < 30 mK',
        'Focus: Manual & minimum distance 0.1m',
        'Video Frame Rate: 50 Hz',
        'Full thermal video recording',
        'Professional documentation',
        'Extended storage capacity',
        'HDMI output capability',
        'Training applications',
        'Comprehensive analysis tools'
      ],
      technicalSpecs: {
        'IR Resolution': '480 × 360 (172,800 pixels)',
        'Video Recording': 'Full thermal video',
        'Temperature Range': '-20°C to 1200°C',
        'Accuracy': '±2°C, ±2%',
        'NETD': '< 30 mK',
        'Focus': 'Manual & minimum distance 0.1m',
        'Frame Rate': '50 Hz',
        'Display': '640 × 480 Resolution, 4" LCD touch screen',
        'Visual Camera': '5 MP with LED flash',
        'Battery': 'Li-ion, 8 hours operation (Interchangeable Battery)',
        'Memory': '128 GB internal + SD card',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54, 2m drop test',
        'Interfaces': 'USB, WiFi, Bluetooth, HDMI',
        'Video Formats': 'Multiple recording formats',
        'Storage Capacity': 'Extended for video recording'
      },
      applications: [
        'Process monitoring',
        'Training and education',
        'Documentation',
        'Research applications',
        'Quality assurance',
        'Professional reporting'
      ],
      advantages: [
        'Video recording capability',
        'Professional documentation',
        'Extended storage',
        'HDMI output',
        'Training applications',
        'Comprehensive analysis'
      ],
      brochures: [
        { name: 'TCC-7460 Brochure', url: '/TCC-7460-(SP40).pdf' },
        { name: 'TCC-742K Brochure', url: '/TCC-742K-(SP40H).pdf' }
      ]
    },
    'tcc-7660': {
      id: 'tcc-7660',
      model: 'TCC 7660 / TCC 762K',
      subtitle: 'High-Res Camcorder',
      image: '/TC-7660-overview.png',
      images: [
        '/TC-7660-overview.png',
        '/TCC-762K-inside-01.png',
        '/TC-7660-inside-02.png'
      ],
      irResolution: '640 × 480',
      temperatureRange: '-20°C to 1200°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'High-resolution thermal camcorder with advanced video recording for professional thermal documentation and analysis.',
      keyFeatures: [
        'IR Resolution: 640 × 480 (307,200 pixels)',
        'High-resolution video recording',
        'Temperature Range: -20°C to 1200°C',
        'Accuracy: ±2°C, ±2%',
        'Professional camcorder features',
        'NETD: < 30 mK',
        'Focus: Manual & minimum distance 0.1m',
        'Video Frame Rate: 50 Hz',
        'High-resolution video capability',
        'Professional features',
        'Advanced documentation',
        'Extended recording time',
        'Superior image quality',
        'Comprehensive connectivity'
      ],
      technicalSpecs: {
        'IR Resolution': '640 × 480 (307,200 pixels)',
        'Video Recording': 'High-resolution thermal video',
        'Temperature Range': '-20°C to 1200°C',
        'Accuracy': '±2°C, ±2%',
        'NETD': '< 30 mK',
        'Focus': 'Manual & minimum distance 0.1m',
        'Frame Rate': '50 Hz',
        'Display': '640 × 480 Resolution, 4" LCD touch screen',
        'Visual Camera': '5 MP with LED flash',
        'Battery': 'Li-ion, 8 hours operation (Interchangeable Battery)',
        'Memory': '128 GB internal + SD card',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54, 2m drop test',
        'Interfaces': 'USB, WiFi, Bluetooth, HDMI',
        'Video Quality': 'High-resolution recording',
        'Professional Grade': 'Advanced features'
      },
      applications: [
        'High-resolution documentation',
        'Professional training',
        'Research and development',
        'Quality control',
        'Process optimization',
        'Advanced analysis'
      ],
      advantages: [
        'High-resolution video',
        'Professional features',
        'Advanced documentation',
        'Extended recording time',
        'Superior image quality',
        'Comprehensive connectivity'
      ],
      brochures: [
        { name: 'TCC-7660 Brochure', url: '/TCC-7660-(SP60).pdf' },
        { name: 'TCC-762K Brochure', url: '/TCC-762K-(SP60H).pdf' }
      ]
    },
    'tcc-812k': {
      id: 'tcc-812k',
      model: 'TCC 812K',
      subtitle: 'Ultra-High Res Camcorder',
      image: '/TC-812k-overview.png',
      images: [
        '/TC-812k-overview.png',
        '/TC-812k-inside.png'
      ],
      irResolution: '1280 × 1024',
      temperatureRange: '-20°C to 1200°C',
      accuracy: '±2°C, ±2%',
      price: 'Contact for pricing',
      description: 'Ultra-high resolution thermal camcorder representing the pinnacle of thermal imaging technology for the most demanding applications.',
      keyFeatures: [
        'IR Resolution: 1280 × 1024 (1,310,720 pixels)',
        'Ultra-high resolution video',
        'Temperature Range: -20°C to 1200°C',
        'Accuracy: ±2°C, ±2%',
        'State-of-the-art technology',
        'NETD: < 20 mK',
        'Focus: Manual & minimum distance 0.1m',
        'Video Frame Rate: 50 Hz',
        'Ultra-high resolution capability',
        'Superior thermal sensitivity',
        'State-of-the-art technology',
        'Extended battery life',
        'Professional-grade features',
        'Industry-leading performance'
      ],
      technicalSpecs: {
        'IR Resolution': '1280 × 1024 (1,310,720 pixels)',
        'Video Recording': 'Ultra-high resolution thermal video',
        'Temperature Range': '-20°C to 1200°C',
        'Accuracy': '±2°C, ±2%',
        'NETD': '< 20 mK',
        'Focus': 'Manual & minimum distance 0.1m',
        'Frame Rate': '50 Hz',
        'Display': '800 × 600 Resolution, 5" LCD touch screen',
        'Visual Camera': '8 MP with LED flash',
        'Battery': 'Li-ion, 10 hours operation (Interchangeable Battery)',
        'Memory': '256 GB internal + SD card',
        'Operating Temperature': '-15°C to 50°C',
        'Storage Temperature': '-40°C to 70°C',
        'Encapsulation': 'IP54, 2m drop test',
        'Interfaces': 'USB, WiFi, Bluetooth, HDMI, Ethernet',
        'Ultra-High Resolution': 'Maximum pixel count',
        'Advanced Technology': 'Latest thermal imaging tech'
      },
      applications: [
        'Research and development',
        'Scientific applications',
        'High-end industrial processes',
        'Quality assurance',
        'Advanced diagnostics',
        'Professional documentation'
      ],
      advantages: [
        'Ultra-high resolution',
        'Superior thermal sensitivity',
        'State-of-the-art technology',
        'Extended battery life',
        'Professional-grade features',
        'Industry-leading performance'
      ],
      brochure: '/TC-812K.pdf'
    }
  };

  const product = productData[productId as keyof typeof productData];

  useEffect(() => {
    if (!product) {
      navigate('/measure/thermal-imagers');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Thermal Imager`;
    }
  }, [product, navigate]);

  // Handle clicking outside dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (dropdownOpen && !target.closest('.dropdown-container')) {
        setDropdownOpen(false);
      }
      if (brochureDropdownOpen && !target.closest('.brochure-dropdown-container')) {
        setBrochureDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownOpen, brochureDropdownOpen]);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Handle image loading errors
  const handleImageError = () => {
    setImageError(true);
  };

  // Get fallback image
  const getFallbackImage = () => {
    return 'https://via.placeholder.com/300x200/FFD700/000000?text=No+Image';
  };

  // Feature icon logic similar to OscilloscopeProduct
  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd') || feature.toLowerCase().includes('screen') || feature.toLowerCase().includes('touch')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage') || feature.toLowerCase().includes('gb') || feature.toLowerCase().includes('sd')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('wifi') || feature.toLowerCase().includes('bluetooth') || feature.toLowerCase().includes('usb') || feature.toLowerCase().includes('connectivity')) return <Wifi className="h-5 w-5" />;
    if (feature.toLowerCase().includes('battery') || feature.toLowerCase().includes('power') || feature.toLowerCase().includes('operation')) return <Battery className="h-5 w-5" />;
    if (feature.toLowerCase().includes('temperature') || feature.toLowerCase().includes('thermal') || feature.toLowerCase().includes('netd')) return <Thermometer className="h-5 w-5" />;
    if (feature.toLowerCase().includes('laser') || feature.toLowerCase().includes('pointer')) return <Zap className="h-5 w-5" />;
    if (feature.toLowerCase().includes('resolution') || feature.toLowerCase().includes('superir') || feature.toLowerCase().includes('frequency')) return <Gauge className="h-5 w-5" />;
    if (feature.toLowerCase().includes('ip54') || feature.toLowerCase().includes('drop') || feature.toLowerCase().includes('rugged') || feature.toLowerCase().includes('protection')) return <Shield className="h-5 w-5" />;
    if (feature.toLowerCase().includes('focus') || feature.toLowerCase().includes('manual') || feature.toLowerCase().includes('fov')) return <Eye className="h-5 w-5" />;
    if (feature.toLowerCase().includes('camera') || feature.toLowerCase().includes('visual') || feature.toLowerCase().includes('mp')) return <Camera className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  // Brochure dropdown logic for products with multiple brochures
  const renderBrochureButton = () => {
    // Check if product has multiple brochures
    if ('brochures' in product && Array.isArray(product.brochures)) {
      return (
        <div className="relative flex-1 brochure-dropdown-container">
          <button
            onClick={() => setBrochureDropdownOpen((open) => !open)}
            className="w-full text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90"
            style={{ backgroundColor: '#F5C842' }}
            type="button"
          >
            <Download className="h-5 w-5" />
            <span>View Brochure</span>
            <ChevronDown className="h-4 w-4 ml-2" />
          </button>
          {brochureDropdownOpen && (
            <div className="absolute left-0 right-0 mt-2 bg-white border border-yellow-400 rounded-xl shadow-lg z-50">
              {product.brochures.map((brochure, index) => (
                <button
                  key={index}
                  onClick={() => { 
                    window.open(brochure.url, '_blank'); 
                    setBrochureDropdownOpen(false); 
                  }}
                  className={`w-full text-left px-4 py-3 hover:bg-yellow-50 transition-colors duration-200 ${
                    index === 0 ? 'rounded-t-xl' : ''
                  } ${
                    index === product.brochures.length - 1 ? 'rounded-b-xl' : 'border-b border-gray-100'
                  }`}
                >
                  {brochure.name}
                </button>
              ))}
            </div>
          )}
        </div>
      );
    } else {
      return (
        <button
          onClick={() => {
            if ('brochure' in product && product.brochure) {
              window.open(product.brochure, '_blank');
            } else {
              window.open('/T&M April 2025.pdf', '_blank');
            }
          }}
          className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90"
          style={{ backgroundColor: '#F5C842' }}
        >
          <Download className="h-5 w-5" />
          <span>View Brochure</span>
        </button>
      );
    }
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            {/* Title always at top in mobile, center in desktop */}
            <div className="text-center mb-4 md:mb-0">
              <h1 className="typography-h1 text-black mb-2">
                Thermal Imagers
              </h1>
              <p className="typography-h4 text-black">
                Professional Thermal Imaging Solutions
              </p>
            </div>
            {/* Responsive flex container for dropdown and back button */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              {/* Dropdown first on mobile, right on desktop */}
              <div className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block dropdown-container">
                <div className="relative w-full md:w-auto group">
                  <button
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className={`h-4 w-4 ml-2 transition-transform duration-200 ${dropdownOpen ? 'rotate-180' : ''}`} />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute top-full left-0 right-0 md:right-auto md:w-80 mt-2 bg-white border border-yellow-400 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => {
                            setDropdownOpen(false);
                            navigate(`/measure/thermal-imagers/product/${prod.id}`);
                          }}
                          className={`w-full text-left px-4 py-3 hover:bg-yellow-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0 ${
                            prod.id === product.id ? 'bg-yellow-50 font-bold' : ''
                          }`}
                        >
                          <div className="font-bold text-black">{prod.model}</div>
                          <div className="text-sm text-gray-600">{prod.subtitle}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Back button second on mobile, left on desktop */}
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={() => navigate('/measure/thermal-imagers')}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-8 md:py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center md:items-stretch gap-6 md:gap-8">
              {/* Content Left (on desktop) */}
              <div className="w-full md:w-1/2 max-w-2xl order-2 md:order-1 flex flex-col justify-center">
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="space-y-4 order-1 lg:order-1"
                >
                  <div className="inline-block px-3 py-1.5 rounded-full text-black font-bold text-xs mb-3" style={{ backgroundColor: '#F5C842' }}>
                    {product.irResolution} IR Resolution
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-black mb-3">
                    {product.model}
                  </h1>
                  <p className="text-lg text-yellow-700 font-semibold mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-base text-black leading-relaxed mb-6">
                    {product.description}
                  </p>
                  {/* Quick Specs */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">IR Resolution</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.irResolution}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Accuracy</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.accuracy}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                      <h4 className="font-semibold text-black mb-1">Temperature Range</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.temperatureRange}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                      <h4 className="font-semibold text-black mb-1">Price</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.price}</p>
                    </div>
                  </div>
                  {/* Action Buttons at Bottom */}
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button onClick={() => navigate('/contact/sales')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Phone className="h-5 w-5" />
                      <span>Request Demo</span>
                    </button>
                    {renderBrochureButton()}
                  </div>
                </motion.div>
              </div>
              {/* Image Carousel Right (on desktop) */}
              <div className="w-full md:w-1/2 flex items-center justify-center order-1 md:order-2 mb-6 md:mb-0">
                <div className="w-full max-w-xs">
                  {product.images && product.images.length > 1 ? (
                    <Carousel
                      images={product.images}
                      className="w-full"
                      theme="yellow"
                    />
                  ) : (
                    <img
                      src={imageError ? getFallbackImage() : product.image}
                      alt={product.model}
                      className="w-full h-auto object-contain"
                      style={{
                        maxHeight: '800px',
                        maxWidth: '800px',
                        background: 'transparent',
                        mixBlendMode: imageError ? 'normal' : 'multiply',
                        filter: imageError ? 'none' : 'brightness(1.1) contrast(1.1)',
                        opacity: '0.95'
                      }}
                      onError={handleImageError}
                      onLoad={() => setImageError(false)}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features and Technical Specifications Section */}
        <div className="py-8 md:py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6">
              <div className="md:col-span-5">
                {/* Key Features Section - Expandable Card */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="w-full bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
                >
                  {/* Header */}
                  <div className="p-6">
                    <h2 className="text-2xl font-bold text-gray-900">Key Features</h2>
                  </div>
                  
                  {/* Content Area - Flex Grow */}
                  <div className="flex-1 flex flex-col">
                    {/* Preview Content - Always Visible */}
                    <div className="px-6 pb-6 space-y-4 flex-1">
                      {product.keyFeatures.slice(0, 6).map((feature, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.05 }}
                          className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                        >
                          <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <FeatureIcon feature={feature} />
                          </div>
                          <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                        </motion.div>
                      ))}
                    </div>
                    
                    {/* Expandable Content - Additional Features */}
                    {product.keyFeatures.length > 6 && (
                      <AnimatePresence>
                        {featuresExpanded && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <div className="px-6 pb-6 space-y-4 border-t border-gray-100 pt-4">
                              {product.keyFeatures.slice(6).map((feature, index) => (
                                <motion.div
                                  key={index + 6}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ duration: 0.4, delay: index * 0.05 }}
                                  className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                                >
                                  <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <FeatureIcon feature={feature} />
                                  </div>
                                  <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                                </motion.div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    )}
                  </div>
                  
                  {/* Show More/Less Button - Always at Bottom */}
                  {product.keyFeatures.length > 6 && (
                    <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                      <button
                        onClick={() => setFeaturesExpanded(!featuresExpanded)}
                        className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                      >
                        {featuresExpanded ? (
                          <>
                            <span>Show Less</span>
                            <ChevronDown className="h-4 w-4" />
                          </>
                        ) : (
                          <>
                            <span>Show {product.keyFeatures.length - 6} More Features</span>
                            <ChevronRight className="h-4 w-4" />
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </motion.div>
              </div>

              {/* Technical Specifications Section - Expandable Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="md:col-span-7 bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
              >
                {/* Header */}
                <div className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900">Technical Specifications</h2>
                </div>
                
                {/* Content Area - Flex Grow */}
                <div className="flex-1 flex flex-col">
                  {/* Preview Content - Always Visible */}
                  <div className="px-6 pb-6 flex-1">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <tbody>
                          {Object.entries(product.technicalSpecs).slice(0, 6).map(([key, value], index) => (
                            <motion.tr
                              key={key}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.4, delay: index * 0.05 }}
                              className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                              }`}
                            >
                              <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                {key}
                              </td>
                              <td className="py-4 px-4 text-gray-700 font-medium">
                                {value}
                              </td>
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  {/* Expandable Content - Additional Specifications */}
                  {Object.entries(product.technicalSpecs).length > 6 && (
                    <AnimatePresence>
                      {specsExpanded && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 border-t border-gray-100 pt-4">
                            <div className="overflow-x-auto">
                              <table className="w-full">
                                <tbody>
                                  {Object.entries(product.technicalSpecs).slice(6).map(([key, value], index) => (
                                    <motion.tr
                                      key={key}
                                      initial={{ opacity: 0, x: -20 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ duration: 0.4, delay: index * 0.05 }}
                                      className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                        index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                                      }`}
                                    >
                                      <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                        {key}
                                      </td>
                                      <td className="py-4 px-4 text-gray-700 font-medium">
                                        {value}
                                      </td>
                                    </motion.tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </div>
                
                {/* Show More/Less Button - Always at Bottom */}
                {Object.entries(product.technicalSpecs).length > 6 && (
                  <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                    <button
                      onClick={() => setSpecsExpanded(!specsExpanded)}
                      className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                    >
                      {specsExpanded ? (
                        <>
                          <span>Show Less</span>
                          <ChevronDown className="h-4 w-4" />
                        </>
                      ) : (
                        <>
                          <span>Show {Object.entries(product.technicalSpecs).length - 6} More Specifications</span>
                          <ChevronRight className="h-4 w-4" />
                        </>
                      )}
                    </button>
                  </div>
                )}
              </motion.div>
            </div>
          </div>
        </div>

        {/* Applications and Advantages Section */}
        <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Applications */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Applications</h2>
                <div className="space-y-3">
                  {product.applications.map((application, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{application}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Advantages */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Advantages</h2>
                <div className="space-y-3">
                  {product.advantages.map((advantage, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{advantage}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-12 md:py-16 mb-16 md:mb-24 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Need Expert Advice?
              </h2>
              <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
                Our specialists provide comprehensive guidance on thermal imaging solutions
              </p>
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto"
                onClick={() => navigate('/contact/sales')}
              >
                <Phone className="h-5 w-5" />
                <span>Contact Sales</span>
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default ThermalImagerProduct;