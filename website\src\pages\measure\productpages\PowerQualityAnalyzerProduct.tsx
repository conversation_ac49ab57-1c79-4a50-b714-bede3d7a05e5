import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Check,
  Download,
  Phone,
  Zap,
  Monitor,
  Database,
  Wifi,
  Battery,
  Thermometer,
  ChevronDown,
  Gauge,
  Shield,
  BarChart,
  ChevronRight
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';
import Carousel from '@/components/Carousel';

const PowerQualityAnalyzerProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [featuresExpanded, setFeaturesExpanded] = useState(false);
  const [specsExpanded, setSpecsExpanded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Product list for dropdown
  const productList = [
    { id: 'alm20', model: 'ALM 20', subtitle: 'Basic Power Quality Analyzer' },
    { id: 'alm31', model: 'ALM 31', subtitle: 'Standard Power Quality Analyzer' },
    { id: 'alm36', model: 'ALM 36', subtitle: 'Advanced Power Quality Analyzer' },
    { id: 'ca8345', model: 'CA 8345', subtitle: 'Premium Power Quality Analyzer' }
  ];

  // Brochure mapping for each product
  const brochureMap: Record<string, string> = {
    alm20: '/alm20p.pdf',
    alm31: '/alm31p.pdf',
    alm36: '/alm36p.pdf',
    ca8345: '/ca8345p.pdf'
  };

  // Complete product data with enhanced features and specs
  const productData = {
    alm20: {
      id: 'alm20',
      model: 'ALM 20',
      subtitle: 'Basic Power Quality Analyzer',
      image: '/alm20_1.jpg',
      images: [
        '/ALM-20-inside-01.png',
        '/ALM-20-inside-02.png' //re images when available
      ],
      voltage: '1,000V Ph-N',
      measurement: 'Power & Energy',
      accuracy: '±0.5%',
      price: 'Contact for pricing',
      description: 'The ALM 20 provides essential power quality analysis in a compact, self-powered package. Perfect for long-term monitoring and basic power quality assessment with reliable performance.',
      keyFeatures: [
        'Compact self-powered design',
        'Long-term monitoring capability',
        'AC/DC voltage measurement up to 1,000V Ph-N',
        'Power measurement: kW, kVAr, kVA, PF & DPF',
        'Energy measurement: kWh, kVArh, kVAh',
        'THD & Harmonics up to 50th order',
        'User-friendly interface',
        'Portable and rugged construction',
        'Battery-powered operation',
        'Real-time data logging',
        'Automatic measurement recording',
        'Environmental protection rating',
        'Easy setup and configuration',
        'Professional measurement accuracy'
      ],
      technicalSpecs: {
        'Voltage Range': 'Up to 1,000V Ph-N AC/DC',
        'Power Measurement': 'kW, kVAr, kVA, PF & DPF',
        'Energy Measurement': 'kWh, kVArh, kVAh',
        'Harmonics': 'THD & up to 50th order',
        'Display': 'LCD with backlight',
        'Power Supply': 'Self-powered',
        'Operating Temperature': '-10°C to +50°C',
        'Storage Temperature': '-20°C to +60°C',
        'Dimensions': '180 x 120 x 60 mm',
        'Weight': '0.8 kg',
        'Memory Capacity': 'Internal data storage',
        'Measurement Rate': 'Continuous monitoring',
        'Safety Rating': 'CAT III 1000V',
        'Accuracy Class': '±0.5% reading',
        'Frequency Range': '45-65 Hz',
        'Current Input': 'Via current clamps',
        'Communication': 'USB interface'
      },
      applications: [
        'Basic power quality monitoring',
        'Energy consumption analysis',
        'Electrical system troubleshooting',
        'Preventive maintenance programs',
        'Small to medium facility monitoring',
        'Educational and training purposes'
      ],
      advantages: [
        'Self-powered operation',
        'Compact portable design',
        'Essential measurement functions',
        'Cost-effective solution',
        'Easy to use interface',
        'Reliable performance'
      ]
    },
    alm31: {
      id: 'alm31',
      model: 'ALM 31',
      subtitle: 'Standard Power Quality Analyzer',
      image: '/alm31_10.jpg',
      images: [
        '/ALM-31-inside-01.png',
        '/ALM-31-inside-02.png'
        // Add more images when available
      ],
      voltage: '1,000V Ph-N',
      measurement: 'Power & Flicker',
      accuracy: '±0.5%',
      price: 'Contact for pricing',
      description: 'The ALM 31 provides real-time visual analysis with its color display and enhanced measurement capabilities for professional power quality assessment with advanced features.',
      keyFeatures: [
        'Color display for enhanced visualization',
        'Real-time power quality analysis',
        'AC/DC voltage measurement up to 1,000V Ph-N',
        'Power measurement: kW, kVAr, kVA, PF & DPF',
        'Crest factor & K-factor measurement',
        'Short flicker measurement (Pst)',
        'Advanced data logging capabilities',
        'Professional-grade construction',
        'Enhanced user interface',
        'Comprehensive alarm system',
        'Multi-parameter display',
        'Trend analysis capability',
        'Export data functionality',
        'Real-time waveform display'
      ],
      technicalSpecs: {
        'Voltage Range': 'Up to 1,000V Ph-N AC/DC',
        'Power Measurement': 'kW, kVAr, kVA, PF & DPF',
        'Crest Factor': 'Measurement capability',
        'K-Factor': 'Measurement capability',
        'Flicker': 'Short flicker (Pst)',
        'Display': 'Color LCD',
        'Data Logging': 'Advanced capability',
        'Operating Temperature': '-10°C to +50°C',
        'Storage Temperature': '-20°C to +60°C',
        'Dimensions': '200 x 140 x 70 mm',
        'Weight': '1.2 kg',
        'Memory': 'Extended storage capacity',
        'Sampling Rate': 'High-speed sampling',
        'Alarm Functions': 'Configurable thresholds',
        'Communication': 'USB and Ethernet',
        'Software': 'PC analysis software',
        'Battery Life': 'Extended operation time',
        'Display Resolution': 'High-definition color'
      },
      applications: [
        'Professional power quality assessment',
        'Industrial facility monitoring',
        'Power system analysis',
        'Flicker measurement studies',
        'Electrical installation verification',
        'Maintenance and troubleshooting'
      ],
      advantages: [
        'Color display visualization',
        'Enhanced measurement capabilities',
        'Professional analysis tools',
        'Advanced data logging',
        'Reliable performance',
        'User-friendly interface'
      ]
    },
    alm36: {
      id: 'alm36',
      model: 'ALM 36',
      subtitle: 'Advanced Power Quality Analyzer',
      image: '/alm-36_1-1.jpg',
      images: [
        '/ALM_36_inside-01.jpg',
        '/ALM_36_inside-02.png'
        // Add more images when available
      ],
      voltage: '1,000V Ph-N',
      measurement: 'Transients & Harmonics',
      accuracy: '±0.2%',
      price: 'Contact for pricing',
      description: 'The ALM 36 provides professional-grade power analysis with IEC compliance, advanced alarms, and transient capture for comprehensive quality assessment in demanding applications.',
      keyFeatures: [
        'Transient capture up to 210 counts',
        'TrueInrush function for motor analysis',
        'THD & Harmonics up to 50th order',
        'Pst & Plt flicker measurements',
        'IEC compliance for professional use',
        'Advanced alarm system',
        'Comprehensive data analysis',
        'Rugged industrial design',
        'High-resolution measurement',
        'Multi-channel analysis',
        'Automatic event detection',
        'Professional reporting tools',
        'Network connectivity options',
        'Advanced triggering system'
      ],
      technicalSpecs: {
        'Voltage Range': 'Up to 1,000V Ph-N AC/DC',
        'Transients': 'Up to 210 counts capture',
        'Harmonics': 'THD & up to 50th order',
        'Flicker': 'Pst & Plt measurements',
        'TrueInrush': 'Motor analysis function',
        'Compliance': 'IEC standards',
        'Alarms': 'Advanced alarm system',
        'Operating Temperature': '-10°C to +55°C',
        'Storage Temperature': '-20°C to +70°C',
        'Dimensions': '220 x 160 x 80 mm',
        'Weight': '1.5 kg',
        'Sampling Rate': 'High-speed transient capture',
        'Memory Depth': 'Extended recording capacity',
        'Trigger Types': 'Multiple trigger options',
        'Analysis Software': 'Professional PC software',
        'Communication': 'USB, Ethernet, WiFi',
        'Data Export': 'Multiple file formats',
        'Display Type': 'Large color touchscreen'
      },
      applications: [
        'Advanced power quality analysis',
        'Industrial equipment monitoring',
        'Motor and drive system analysis',
        'Power system commissioning',
        'Compliance testing and verification',
        'Critical facility monitoring'
      ],
      advantages: [
        'Advanced transient capture',
        'IEC compliance',
        'TrueInrush functionality',
        'Comprehensive analysis',
        'Professional-grade accuracy',
        'Industrial durability'
      ]
    },
    ca8345: {
      id: 'ca8345',
      model: 'CA 8345',
      subtitle: 'Premium Power Quality Analyzer',
      image: '/ca8345_qualistar_f.jpg',
      images: [
        '/ca8345_qualistar_f.jpg',
        '/ca8345_qualistar_g.jpg'// Add more images when available
      ],
      voltage: '1,000V Ph-N',
      measurement: 'Class A Certified',
      accuracy: '±0.1%',
      price: 'Contact for pricing',
      description: 'The flagship CA 8345 delivers industry-leading performance with Class A certification, advanced harmonic analysis, and superior storage capacity for the most demanding applications.',
      keyFeatures: [
        'Class A certified performance',
        'Harmonics up to 127th order',
        'Interharmonics up to 126th order',
        '2.5 μs transients capture',
        '16,362 alarms of 40 conditions',
        'Superior data storage capacity',
        'Advanced PC software included',
        'Professional certification compliance',
        'Ultra-high precision measurement',
        'Real-time spectrum analysis',
        'Advanced power quality reporting',
        'Multi-site monitoring capability',
        'Professional data export tools',
        'Industry-leading accuracy specifications'
      ],
      technicalSpecs: {
        'Certification': 'Class A certified',
        'Harmonics': 'Up to 127th order',
        'Interharmonics': 'Up to 126th order',
        'Transients': '2.5 μs capture capability',
        'Alarms': '16,362 alarms, 40 conditions',
        'Accuracy': '±0.1%',
        'Storage': 'Superior capacity',
        'Software': 'Advanced PC interface',
        'Operating Temperature': '-10°C to +55°C',
        'Storage Temperature': '-20°C to +70°C',
        'Dimensions': '250 x 180 x 90 mm',
        'Weight': '2.0 kg',
        'Sampling Rate': 'Ultra-high speed',
        'Memory Capacity': 'Maximum storage available',
        'Display': 'Large high-resolution touchscreen',
        'Communication': 'Multiple connectivity options',
        'Compliance Standards': 'IEC 61000-4-30 Class A',
        'Measurement Channels': 'Multi-channel capability',
        'Data Analysis': 'Professional analysis suite'
      },
      applications: [
        'Premium power quality analysis',
        'Utility and grid monitoring',
        'Research and development',
        'High-precision measurements',
        'Certification and compliance testing',
        'Critical infrastructure monitoring'
      ],
      advantages: [
        'Class A certification',
        'Industry-leading accuracy',
        'Advanced harmonic analysis',
        'Superior alarm system',
        'Professional compliance',
        'Maximum storage capacity'
      ]
    }
  };

  const product = productData[productId as keyof typeof productData];

  useEffect(() => {
    if (!product) {
      navigate('/measure/power-quality-analyzers');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Power Quality Analyzer`;
    }
  }, [product, navigate]);

  // Handle clicking outside dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (dropdownOpen && !target.closest('.dropdown-container')) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownOpen]);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Handle image loading errors
  const handleImageError = () => {
    setImageError(true);
  };

  // Get fallback image
  const getFallbackImage = () => {
    return 'https://via.placeholder.com/300x200/FFD700/000000?text=No+Image';
  };

  // Feature icon logic similar to OscilloscopeProduct
  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd') || feature.toLowerCase().includes('color') || feature.toLowerCase().includes('touchscreen')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage') || feature.toLowerCase().includes('logging') || feature.toLowerCase().includes('data')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('communication') || feature.toLowerCase().includes('usb') || feature.toLowerCase().includes('ethernet') || feature.toLowerCase().includes('wifi') || feature.toLowerCase().includes('connectivity')) return <Wifi className="h-5 w-5" />;
    if (feature.toLowerCase().includes('power') || feature.toLowerCase().includes('battery') || feature.toLowerCase().includes('self-powered')) return <Battery className="h-5 w-5" />;
    if (feature.toLowerCase().includes('temperature') || feature.toLowerCase().includes('environmental')) return <Thermometer className="h-5 w-5" />;
    if (feature.toLowerCase().includes('voltage') || feature.toLowerCase().includes('current') || feature.toLowerCase().includes('transient') || feature.toLowerCase().includes('alarm')) return <Zap className="h-5 w-5" />;
    if (feature.toLowerCase().includes('measurement') || feature.toLowerCase().includes('accuracy') || feature.toLowerCase().includes('harmonics') || feature.toLowerCase().includes('analysis')) return <Gauge className="h-5 w-5" />;
    if (feature.toLowerCase().includes('safety') || feature.toLowerCase().includes('compliance') || feature.toLowerCase().includes('iec') || feature.toLowerCase().includes('certified')) return <Shield className="h-5 w-5" />;
    if (feature.toLowerCase().includes('flicker') || feature.toLowerCase().includes('trend') || feature.toLowerCase().includes('spectrum') || feature.toLowerCase().includes('waveform')) return <BarChart className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  // Get the brochure URL for the current product, fallback to undefined if not found
  const brochureUrl = brochureMap[product.id];

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            {/* Title always at top in mobile, center in desktop */}
            <div className="text-center mb-4 md:mb-0">
              <h1 className="typography-h1 text-black mb-2">
                Power Quality Analyzers
              </h1>
              <p className="typography-h4 text-black">
                Professional Power Analysis Solutions
              </p>
            </div>
            {/* Responsive flex container for dropdown and back button */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              {/* Dropdown first on mobile, right on desktop */}
              <div className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block dropdown-container">
                <div className="relative w-full md:w-auto group">
                  <button
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className={`h-4 w-4 ml-2 transition-transform duration-200 ${dropdownOpen ? 'rotate-180' : ''}`} />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute top-full left-0 right-0 md:right-auto md:w-80 mt-2 bg-white border border-yellow-400 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => {
                            setDropdownOpen(false);
                            navigate(`/measure/power-quality-analyzers/product/${prod.id}`);
                          }}
                          className={`w-full text-left px-4 py-3 hover:bg-yellow-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0 ${
                            prod.id === product.id ? 'bg-yellow-50 font-bold' : ''
                          }`}
                        >
                          <div className="font-bold text-black">{prod.model}</div>
                          <div className="text-sm text-gray-600">{prod.subtitle}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Back button second on mobile, left on desktop */}
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={() => navigate('/measure/power-quality-analyzers')}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-8 md:py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center md:items-stretch gap-6 md:gap-8">
              {/* Content Left (on desktop) */}
              <div className="w-full md:w-1/2 max-w-2xl order-2 md:order-1 flex flex-col justify-center">
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="space-y-4 order-1 lg:order-1"
                >
                  <div className="inline-block px-3 py-1.5 rounded-full text-black font-bold text-xs mb-3" style={{ backgroundColor: '#F5C842' }}>
                    {product.measurement}
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-black mb-3">
                    {product.model}
                  </h1>
                  <p className="text-lg text-yellow-700 font-semibold mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-base text-black leading-relaxed mb-6">
                    {product.description}
                  </p>
                  {/* Quick Specs */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Voltage Range</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.voltage}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Accuracy</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.accuracy}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                      <h4 className="font-semibold text-black mb-1">Price</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.price}</p>
                    </div>
                  </div>
                  {/* Action Buttons at Bottom */}
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button onClick={() => navigate('/contact/sales')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Phone className="h-5 w-5" />
                      <span>Request Demo</span>
                    </button>
                    {brochureUrl && (
                      <button
                        onClick={() => window.open(brochureUrl, '_blank')}
                        className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90"
                        style={{ backgroundColor: '#F5C842' }}
                      >
                        <Download className="h-5 w-5" />
                        <span>View Brochure</span>
                      </button>
                    )}
                  </div>
                </motion.div>
              </div>
              {/* Image Carousel Right (on desktop) */}
              <div className="w-full md:w-1/2 flex items-center justify-center order-1 md:order-2 mb-6 md:mb-0">
                <div
                  className={`w-full ${product.id === 'alm20' ? '' : 'max-w-xs'}`}
                  style={
                    product.id === 'alm20'
                      ? { maxWidth: '350px' } // Increased from 220px to 350px
                      : {}
                  }
                >
                  {product.images && product.images.length > 1 ? (
                    <Carousel
                      images={product.images}
                      className="w-full"
                      theme="yellow"
                      imageStyle={
                        product.id === 'alm20'
                          ? {
                              width: '100%',
                              maxWidth: '350px',
                              height: 'auto',
                              background: 'transparent',
                              mixBlendMode: imageError ? 'normal' : 'multiply',
                              filter: imageError ? 'none' : 'brightness(1.1) contrast(1.1)',
                              opacity: '0.95',
                            }
                          : undefined
                      }
                    />
                  ) : (
                    <img
                      src={imageError ? getFallbackImage() : product.image}
                      alt={product.model}
                      className="object-contain"
                      style={
                        product.id === 'alm20'
                          ? {
                              width: '350px', // Increased from 800px to 350px for a more reasonable large size
                              height: '350px',
                              background: 'transparent',
                              mixBlendMode: imageError ? 'normal' : 'multiply',
                              filter: imageError ? 'none' : 'brightness(1.1) contrast(1.1)',
                              opacity: '0.95',
                            }
                          : {
                              maxHeight: '800px',
                              maxWidth: '800px',
                              background: 'transparent',
                              mixBlendMode: imageError ? 'normal' : 'multiply',
                              filter: imageError ? 'none' : 'brightness(1.1) contrast(1.1)',
                              opacity: '0.95',
                            }
                      }
                      onError={handleImageError}
                      onLoad={() => setImageError(false)}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features and Technical Specifications Section */}
        <div className="py-8 md:py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6">
              <div className="md:col-span-5">
                {/* Key Features Section - Expandable Card */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="w-full bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
                >
                  {/* Header */}
                  <div className="p-6">
                    <h2 className="text-2xl font-bold text-gray-900">Key Features</h2>
                  </div>
                  
                  {/* Content Area - Flex Grow */}
                  <div className="flex-1 flex flex-col">
                    {/* Preview Content - Always Visible */}
                    <div className="px-6 pb-6 space-y-4 flex-1">
                      {product.keyFeatures.slice(0, 6).map((feature, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.05 }}
                          className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                        >
                          <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <FeatureIcon feature={feature} />
                          </div>
                          <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                        </motion.div>
                      ))}
                    </div>
                    
                    {/* Expandable Content - Additional Features */}
                    {product.keyFeatures.length > 6 && (
                      <AnimatePresence>
                        {featuresExpanded && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <div className="px-6 pb-6 space-y-4 border-t border-gray-100 pt-4">
                              {product.keyFeatures.slice(6).map((feature, index) => (
                                <motion.div
                                  key={index + 6}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ duration: 0.4, delay: index * 0.05 }}
                                  className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                                >
                                  <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <FeatureIcon feature={feature} />
                                  </div>
                                  <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                                </motion.div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    )}
                  </div>
                  
                  {/* Show More/Less Button - Always at Bottom */}
                  {product.keyFeatures.length > 6 && (
                    <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                      <button
                        onClick={() => setFeaturesExpanded(!featuresExpanded)}
                        className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                      >
                        {featuresExpanded ? (
                          <>
                            <span>Show Less</span>
                            <ChevronDown className="h-4 w-4" />
                          </>
                        ) : (
                          <>
                            <span>Show {product.keyFeatures.length - 6} More Features</span>
                            <ChevronRight className="h-4 w-4" />
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </motion.div>
              </div>

              {/* Technical Specifications Section - Expandable Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="md:col-span-7 bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
              >
                {/* Header */}
                <div className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900">Technical Specifications</h2>
                </div>
                
                {/* Content Area - Flex Grow */}
                <div className="flex-1 flex flex-col">
                  {/* Preview Content - Always Visible */}
                  <div className="px-6 pb-6 flex-1">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <tbody>
                          {Object.entries(product.technicalSpecs).slice(0, 6).map(([key, value], index) => (
                            <motion.tr
                              key={key}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.4, delay: index * 0.05 }}
                              className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                              }`}
                            >
                              <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                {key}
                              </td>
                              <td className="py-4 px-4 text-gray-700 font-medium">
                                {value}
                              </td>
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  {/* Expandable Content - Additional Specifications */}
                  {Object.entries(product.technicalSpecs).length > 6 && (
                    <AnimatePresence>
                      {specsExpanded && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 border-t border-gray-100 pt-4">
                            <div className="overflow-x-auto">
                              <table className="w-full">
                                <tbody>
                                  {Object.entries(product.technicalSpecs).slice(6).map(([key, value], index) => (
                                    <motion.tr
                                      key={key}
                                      initial={{ opacity: 0, x: -20 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ duration: 0.4, delay: index * 0.05 }}
                                      className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                        index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                                      }`}
                                    >
                                      <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                        {key}
                                      </td>
                                      <td className="py-4 px-4 text-gray-700 font-medium">
                                        {value}
                                      </td>
                                    </motion.tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </div>
                
                {/* Show More/Less Button - Always at Bottom */}
                {Object.entries(product.technicalSpecs).length > 6 && (
                  <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                    <button
                      onClick={() => setSpecsExpanded(!specsExpanded)}
                      className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                    >
                      {specsExpanded ? (
                        <>
                          <span>Show Less</span>
                          <ChevronDown className="h-4 w-4" />
                        </>
                      ) : (
                        <>
                          <span>Show {Object.entries(product.technicalSpecs).length - 6} More Specifications</span>
                          <ChevronRight className="h-4 w-4" />
                        </>
                      )}
                    </button>
                  </div>
                )}
              </motion.div>
            </div>
          </div>
        </div>

        {/* Applications and Advantages Section */}
        <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Applications */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Applications</h2>
                <div className="space-y-3">
                  {product.applications.map((application, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{application}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Advantages */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Advantages</h2>
                <div className="space-y-3">
                  {product.advantages.map((advantage, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{advantage}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-12 md:py-16 mb-16 md:mb-24 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Need Expert Advice?
              </h2>
              <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
                Our specialists provide comprehensive guidance on power quality analyzer solutions
              </p>
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto"
                onClick={() => navigate('/contact/sales')}
              >
                <Phone className="h-5 w-5" />
                <span>Contact Sales</span>
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default PowerQualityAnalyzerProduct;