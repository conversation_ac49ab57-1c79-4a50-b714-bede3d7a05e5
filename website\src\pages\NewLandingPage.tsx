import React from 'react';
import Layout from "@/components/layout/Layout";
import HeroAtandra from "@/components/HeroAtandra";
import ServicesFixed from "@/components/ServicesFixed";
import TimelineFixed from "@/components/TimelineFixed";
import ClientLogosSection from "@/components/ClientLogosSection";
import FloatingVoiceWidget from '@/components/FloatingVoiceWidget';

const NewLandingPage = () => {
  return (
    <Layout>
      <div className="min-h-screen overflow-x-hidden">
        {/* Hero Section */}
        <HeroAtandra />

        {/* Services Section */}
        <ServicesFixed />

        {/* Timeline Section */}
        <TimelineFixed />

        {/* Client Logos Section */}
        <ClientLogosSection isInView={true} />

        {/* Floating Voice Widget */}
        <FloatingVoiceWidget />
      </div>
    </Layout>
  );
};

export default NewLandingPage;
