#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1.5rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Updated gradient backgrounds with pure yellow */
.bg-gradient-primary {
  background: linear-gradient(135deg, #FFFF00 0%, #FFD700 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #FFFF00 0%, #FFE135 100%);
}

/* Text styles */
.text-gradient {
  background: linear-gradient(135deg, #FFFF00 0%, #FFD700 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Button hover effects */
.btn-primary-hover:hover {
  filter: brightness(1.1);
  transform: translateY(-2px);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(255, 255, 0, 0.1);
}

/* Additional utility classes */
.bg-primary-light {
  background-color: rgba(255, 255, 0, 0.1);
}

.border-primary {
  border-color: #FFFF00;
}

/* Ensure smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Override existing theme colors with pure yellow scheme */
:root {
  --primary: #FFFF00;
  --primary-dark: #FFD700;
  --primary-light: #FFE135;
  --text-primary: #2C3E50;
  --text-secondary: #566573;
}
