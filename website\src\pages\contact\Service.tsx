"use client";

import React, { useRef, useState, useEffect } from "react";
import { motion, useInView } from "framer-motion";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import {
  Search,
  Calendar,
  Clock,
  ChevronDown,
  MapPin,
  Phone,
  Mail,
  Building,
  User,
  MessageSquare,
  FileText,
  Wrench,
  Send,
  Navigation,
  AtSign,
  X,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

const Service = () => {
  const [activeTab, setActiveTab] = useState('register');
  const formRef = useRef(null);
  const datePickerRef = useRef<HTMLDivElement>(null);
  const timePickerRef = useRef<HTMLDivElement>(null);
  const formInView = useInView(formRef, { amount: 0.1, once: true });

  // State for form inputs to handle floating labels
  const [formInputs, setFormInputs] = useState({
    serialNumber: '',
    productCategory: '',
    rating: '',
    model: '',
    brand: '',
    company: '',
    companyAddress: '',
    person: '',
    personContact: '',
    mobileNo: '',
    email: '',
    serviceCenter: '',
    callType: '',
    callCategory: '',
    currentCondition: '',
    preferredTime: '',
    problemDetails: ''
  });

  // State for date and time pickers
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');

  // Calendar state
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [calendarSelectedDate, setCalendarSelectedDate] = useState<Date | null>(null);

  // Handle clicking outside dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // For calendar modal, we handle backdrop clicks separately
      if (timePickerRef.current && !timePickerRef.current.contains(event.target as Node)) {
        setShowTimePicker(false);
      }
    };

    // Only add listener for time picker since calendar has backdrop
    if (showTimePicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showTimePicker]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setFormInputs(prev => ({
      ...prev,
      [id]: value
    }));
  };



  // Handle time selection
  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    updatePreferredTime(selectedDate, time);
    setShowTimePicker(false);
  };

  // Update preferred time display
  const updatePreferredTime = (date: string, time: string) => {
    let displayValue = '';
    if (date && time) {
      const dateObj = new Date(date);
      const formattedDate = dateObj.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      });
      const timeObj = time.split(':');
      const hour = parseInt(timeObj[0]);
      const minute = timeObj[1];
      const formattedTime = `${hour > 12 ? hour - 12 : hour}:${minute} ${hour >= 12 ? 'PM' : 'AM'}`;
      displayValue = `${formattedDate} at ${formattedTime}`;
    } else if (date) {
      const dateObj = new Date(date);
      displayValue = dateObj.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      });
    } else if (time) {
      const timeObj = time.split(':');
      const hour = parseInt(timeObj[0]);
      const minute = timeObj[1];
      const formattedTime = `${hour > 12 ? hour - 12 : hour}:${minute} ${hour >= 12 ? 'PM' : 'AM'}`;
      displayValue = `Time: ${formattedTime}`;
    }

    setFormInputs(prev => ({
      ...prev,
      preferredTime: displayValue
    }));
  };

  // Clear preferred time
  const clearPreferredTime = () => {
    setSelectedDate('');
    setSelectedTime('');
    setFormInputs(prev => ({
      ...prev,
      preferredTime: ''
    }));
    setShowDatePicker(false);
    setShowTimePicker(false);
  };

  // Generate time slots
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 9; hour <= 18; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        const displayTime = `${hour > 12 ? hour - 12 : hour}:${minute.toString().padStart(2, '0')} ${hour >= 12 ? 'PM' : 'AM'}`;
        slots.push({ value: timeString, display: displayTime });
      }
    }
    return slots;
  };

  // Calendar helper functions
  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1).getDay();
    // Convert Sunday (0) to be 6, and Monday (1) to be 0
    return firstDay === 0 ? 6 : firstDay - 1;
  };

  const getMonthNames = () => {
    return [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
  };

  const getYearRange = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = currentYear; year <= currentYear + 10; year++) {
      years.push(year);
    }
    return years;
  };

  const handleMonthChange = (monthIndex: number) => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(monthIndex);
      return newDate;
    });
  };

  const handleYearChange = (year: number) => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      newDate.setFullYear(year);
      return newDate;
    });
  };

  const goToToday = () => {
    setCurrentMonth(new Date());
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(newDate.getMonth() - 1);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      return newDate;
    });
  };

  const handleCalendarDateSelect = (day: number) => {
    const selectedDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Don't allow selecting past dates
    if (selectedDate < today) return;

    setCalendarSelectedDate(selectedDate);
    const dateString = selectedDate.toISOString().split('T')[0];
    setSelectedDate(dateString);
    updatePreferredTime(dateString, selectedTime);
    setShowDatePicker(false);
  };

  const isDateDisabled = (day: number) => {
    const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  const isDateSelected = (day: number) => {
    if (!calendarSelectedDate) return false;
    const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    return date.toDateString() === calendarSelectedDate.toDateString();
  };

  const isToday = (day: number) => {
    const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const renderCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentMonth);
    const firstDay = getFirstDayOfMonth(currentMonth);
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      const prevMonth = new Date(currentMonth);
      prevMonth.setMonth(prevMonth.getMonth() - 1);
      const prevMonthDays = getDaysInMonth(prevMonth);
      const day = prevMonthDays - firstDay + i + 1;

      days.push(
        <button
          key={`prev-${day}`}
          className="w-8 h-8 text-gray-600 text-xs font-medium rounded-lg"
          disabled
        >
          {day}
        </button>
      );
    }

    // Add days of current month
    for (let day = 1; day <= daysInMonth; day++) {
      const isDisabled = isDateDisabled(day);
      const isSelected = isDateSelected(day);
      const isTodayDate = isToday(day);

      days.push(
        <button
          key={day}
          onClick={() => !isDisabled && handleCalendarDateSelect(day)}
          disabled={isDisabled}
          className={`w-8 h-8 rounded-xl transition-all duration-300 text-xs font-bold relative overflow-hidden group ${
            isSelected
              ? 'bg-white text-black shadow-lg transform scale-110 ring-2 ring-white ring-offset-2 ring-offset-black'
              : isTodayDate && !isDisabled
              ? 'bg-gray-800 text-white border-2 border-white hover:bg-gray-700 shadow-md'
              : isDisabled
              ? 'text-gray-600 cursor-not-allowed opacity-50'
              : 'text-white hover:bg-gray-800 hover:text-white hover:scale-110 hover:shadow-md border border-gray-700 hover:border-gray-600'
          }`}
        >
          <span className="relative z-10">{day}</span>
          {!isDisabled && !isSelected && (
            <div className="absolute inset-0 bg-gradient-to-br from-gray-800/0 to-gray-600/0 group-hover:from-gray-800/50 group-hover:to-gray-600/50 transition-all duration-300" />
          )}
        </button>
      );
    }

    return days;
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  return (
    <PageLayout
      hideHero
      hideBreadcrumbs
    >
      <div className="max-w-7xl mx-auto px-2 sm:px-4 py-8 sm:py-12 relative overflow-hidden">
        {/* Simplified background decorative elements */}
        <div className="absolute top-0 right-0 w-40 sm:w-64 h-40 sm:h-64 bg-black/5 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-60 sm:w-96 h-60 sm:h-96 bg-gray-900/5 rounded-full filter blur-3xl"></div>

        <div
          ref={formRef}
          className="relative"
        >
          {/* Animated background elements - subtle and non-intrusive */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(4)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full opacity-5"
                style={{
                  width: 80 + Math.random() * 120, // smaller on mobile
                  height: 80 + Math.random() * 120,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  background: i % 3 === 0
                    ? "radial-gradient(circle, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 70%)"
                    : i % 3 === 1
                      ? "radial-gradient(circle, rgba(64, 64, 64, 0.6) 0%, rgba(32, 32, 32, 0) 70%)"
                      : "radial-gradient(circle, rgba(128, 128, 128, 0.6) 0%, rgba(96, 96, 96, 0) 70%)",
                }}
                animate={{
                  scale: [1, 1.1, 1],
                  opacity: [0.05, 0.08, 0.05],
                }}
                transition={{
                  duration: 12 + Math.random() * 10,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={formInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8 sm:mb-10"
          >
            <span className="inline-block bg-gradient-to-r from-black via-gray-800 to-gray-900 text-white px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-medium mb-2 sm:mb-3 border border-white/20 shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-default">
              Service Registration
            </span>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 text-black drop-shadow-md">
              Let's Address Your <span className="bg-clip-text text-transparent bg-gradient-to-r from-black via-gray-800 to-gray-900 animate-gradient">Service Needs</span>
            </h2>
            <p className="text-base sm:text-lg text-black max-w-2xl mx-auto font-medium drop-shadow">
              Please register your complaint in the form below, with as many details as possible. Our service team will contact you promptly.
            </p>
          </motion.div>

          <div className="flex border-b border-gray-200 mb-6 sm:mb-8 justify-center">
            <button
              className={`pb-2 px-4 sm:px-6 text-sm sm:text-base ${activeTab === 'register' ? 'text-black border-b-2 border-black font-medium' : 'text-gray-600'} transition-all duration-300 hover:text-gray-800`}
              onClick={() => setActiveTab('register')}
            >
              Log Service Call
            </button>
            <button
              className={`pb-2 px-4 sm:px-6 text-sm sm:text-base ${activeTab === 'track' ? 'text-black border-b-2 border-black font-medium' : 'text-gray-600'} transition-all duration-300 hover:text-gray-800`}
              onClick={() => setActiveTab('track')}
            >
              Track Service Call
            </button>
          </div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={formInView ? "visible" : "hidden"}
            className="grid grid-cols-1 lg:grid-cols-12 gap-6 sm:gap-8"
          >
            {/* Form Section - 8 columns on large screens */}
            <motion.div
              variants={itemVariants}
              className="lg:col-span-8 space-y-4 sm:space-y-6 transition-all duration-700 hover:scale-[1.01]"
            >
              {/* Product Details */}
              <div className="p-4 sm:p-8 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-gray-100/50 hover:border-gray-200 transition-all duration-300 hover:shadow-gray-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-8 sm:-top-10 -right-8 sm:-right-10 w-16 sm:w-20 h-16 sm:h-20 bg-black/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-8 sm:-bottom-10 -left-8 sm:-left-10 w-16 sm:w-20 h-16 sm:h-20 bg-gray-900/10 rounded-full blur-xl"></div>

                {/* Header with shine effect */}
                <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-gray-50/50 to-transparent">
                  <h3 className="text-xl font-bold text-black py-3 px-2 sm:px-4 drop-shadow-sm flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-black" />
                    Product Details
                  </h3>
                  {/* Enhanced shine effect */}
                  <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                  <div className="space-y-2">
                    <label htmlFor="serialNumber" className="block text-sm font-medium text-black">
                      Serial Number
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <FileText className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="serialNumber"
                            value={formInputs.serialNumber}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-gray-300/70 rounded-md text-black placeholder-transparent focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 hover:border-gray-400 peer"
                            placeholder="Enter serial number"
                          />
                          <label
                            htmlFor="serialNumber"
                            className={`absolute text-sm text-black duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.serialNumber ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter serial number
                          </label>
                        </div>
                        <button className="absolute right-3 top-1/2 -translate-y-1/2 text-black hover:text-gray-800 transition-colors">
                          <Search size={18} />
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="productCategory" className="block text-sm font-medium text-black">
                      Select Product Category
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Wrench className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="productCategory"
                            value={formInputs.productCategory}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-gray-300/70 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-black appearance-none transition-all duration-300 hover:border-gray-400"
                          >
                            <option value="">Select a category</option>
                            <option value="energy-meters">Energy Meters</option>
                            <option value="protection-relays">Protection Relays</option>
                            <option value="monitoring-systems">Monitoring Systems</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-black pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="rating" className="block text-sm font-medium text-black">
                      Rating
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <FileText className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="rating"
                            value={formInputs.rating}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-gray-300/70 rounded-md text-black placeholder-transparent focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 hover:border-gray-400 peer"
                            placeholder="Enter rating"
                          />
                          <label
                            htmlFor="rating"
                            className={`absolute text-sm text-black duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.rating ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter rating
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="model" className="block text-sm font-medium text-black">
                      Model
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Wrench className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="model"
                            value={formInputs.model}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-gray-300/70 rounded-md text-black placeholder-transparent focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 hover:border-gray-400 peer"
                            placeholder="Enter model"
                          />
                          <label
                            htmlFor="model"
                            className={`absolute text-sm text-black duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.model ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter model
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="md:col-span-2 space-y-2">
                    <label htmlFor="brand" className="block text-sm font-medium text-black">
                      Select Brand
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Building className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="brand"
                            value={formInputs.brand}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-gray-300/70 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-black appearance-none transition-all duration-300 hover:border-gray-400"
                          >
                            <option value="">Select a brand</option>
                            <option value="krykard">Krykard</option>
                            <option value="other">Other</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-black pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Customer Details */}
              <div className="p-4 sm:p-8 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-gray-100/50 hover:border-gray-200 transition-all duration-300 hover:shadow-gray-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-8 sm:-top-10 -right-8 sm:-right-10 w-16 sm:w-20 h-16 sm:h-20 bg-black/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-8 sm:-bottom-10 -left-8 sm:-left-10 w-16 sm:w-20 h-16 sm:h-20 bg-gray-900/10 rounded-full blur-xl"></div>

                {/* Header with shine effect */}
                <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-gray-50/50 to-transparent">
                  <h3 className="text-xl font-bold text-black py-3 px-2 sm:px-4 drop-shadow-sm flex items-center">
                    <User className="h-5 w-5 mr-2 text-black" />
                    Customer Details
                  </h3>
                  {/* Enhanced shine effect */}
                  <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                  <div className="space-y-2">
                    <label htmlFor="company" className="block text-sm font-medium text-black">
                      Company
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Building className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="company"
                            value={formInputs.company}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-gray-300/70 rounded-md text-black placeholder-transparent focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 hover:border-gray-400 peer"
                            placeholder="Enter company name"
                          />
                          <label
                            htmlFor="company"
                            className={`absolute text-sm text-black duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.company ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter company name
                          </label>
                        </div>
                        <button className="absolute right-3 top-1/2 -translate-y-1/2 text-black hover:text-gray-800 transition-colors">
                          <Search size={18} />
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="companyAddress" className="block text-sm font-medium text-black">
                      Company Address
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <MapPin className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="companyAddress"
                            value={formInputs.companyAddress}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-gray-300/70 rounded-md text-black placeholder-transparent focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 hover:border-gray-400 peer"
                            placeholder="Enter company address"
                          />
                          <label
                            htmlFor="companyAddress"
                            className={`absolute text-sm text-black duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.companyAddress ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter company address
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="person" className="block text-sm font-medium text-black">
                      Person
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <User className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="person"
                            value={formInputs.person}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-gray-300/70 rounded-md text-black placeholder-transparent focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 hover:border-gray-400 peer"
                            placeholder="Enter contact person"
                          />
                          <label
                            htmlFor="person"
                            className={`absolute text-sm text-black duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.person ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter contact person
                          </label>
                        </div>
                        <button className="absolute right-3 top-1/2 -translate-y-1/2 text-black hover:text-gray-700 transition-colors">
                          <Search size={18} />
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="personContact" className="block text-sm font-medium text-black">
                      Person
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <User className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="personContact"
                            value={formInputs.personContact}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-gray-300/70 rounded-md text-black placeholder-transparent focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 hover:border-gray-400 peer"
                            placeholder="Enter alternative contact"
                          />
                          <label
                            htmlFor="personContact"
                            className={`absolute text-sm text-black duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.personContact ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter alternative contact
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="mobileNo" className="block text-sm font-medium text-black">
                      Mobile No
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Phone className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="tel"
                            id="mobileNo"
                            value={formInputs.mobileNo}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-gray-300/70 rounded-md text-black placeholder-transparent focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 hover:border-gray-400 peer"
                            placeholder="Enter mobile number"
                          />
                          <label
                            htmlFor="mobileNo"
                            className={`absolute text-sm text-black duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.mobileNo ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter mobile number
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="email" className="block text-sm font-medium text-black">
                      Email
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Mail className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="email"
                            id="email"
                            value={formInputs.email}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-gray-300/70 rounded-md text-black placeholder-transparent focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 hover:border-gray-400 peer"
                            placeholder="Enter email address"
                          />
                          <label
                            htmlFor="email"
                            className={`absolute text-sm text-black duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.email ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Enter email address
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* General Section */}
              <div className="p-4 sm:p-10 pb-16 sm:pb-20 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-gray-100/50 hover:border-gray-200 transition-all duration-300 hover:shadow-gray-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-8 sm:-top-10 -right-8 sm:-right-10 w-16 sm:w-20 h-16 sm:h-20 bg-black/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-8 sm:-bottom-10 -left-8 sm:-left-10 w-16 sm:w-20 h-16 sm:h-20 bg-gray-900/10 rounded-full blur-xl"></div>

                {/* Header with shine effect */}
                <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-gray-50/50 to-transparent">
                  <h3 className="text-xl font-bold text-black py-3 px-2 sm:px-4 drop-shadow-sm flex items-center">
                    <Wrench className="h-5 w-5 mr-2 text-black" />
                    General
                  </h3>
                  {/* Enhanced shine effect */}
                  <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                  <div className="space-y-2">
                    <label htmlFor="serviceCenter" className="block text-sm font-medium text-black">
                      Service Center
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Building className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="serviceCenter"
                            value={formInputs.serviceCenter}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-gray-300/70 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-black appearance-none transition-all duration-300 hover:border-gray-400"
                          >
                            <option value="">Select service center</option>
                            <option value="chennai">Chennai</option>
                            <option value="mumbai">Mumbai</option>
                            <option value="delhi">Delhi</option>
                            <option value="bangalore">Bangalore</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-black pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="callType" className="block text-sm font-medium text-black">
                      Call Type
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Phone className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="callType"
                            value={formInputs.callType}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-gray-300/70 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-black appearance-none transition-all duration-300 hover:border-gray-400"
                          >
                            <option value="">Select call type</option>
                            <option value="installation">Installation</option>
                            <option value="repair">Repair</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="inquiry">Inquiry</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-black pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="callCategory" className="block text-sm font-medium text-black">
                      Call Category
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <MessageSquare className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="callCategory"
                            value={formInputs.callCategory}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-gray-300/70 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-black appearance-none transition-all duration-300 hover:border-gray-400"
                          >
                            <option value="">Select category</option>
                            <option value="urgent">Urgent</option>
                            <option value="normal">Normal</option>
                            <option value="scheduled">Scheduled</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-black pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="currentCondition" className="block text-sm font-medium text-black">
                      Select Current Condition
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Wrench className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <select
                            id="currentCondition"
                            value={formInputs.currentCondition}
                            onChange={handleInputChange}
                            className="w-full py-3 pl-3 pr-10 bg-white/90 border border-gray-300/70 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-black appearance-none transition-all duration-300 hover:border-gray-400"
                          >
                            <option value="">Select condition</option>
                            <option value="working">Working with issues</option>
                            <option value="not-working">Not working</option>
                            <option value="intermittent">Intermittent problems</option>
                            <option value="new-install">New installation</option>
                          </select>
                          <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-black pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="preferredTime" className="block text-sm font-medium text-black">
                      Preferred Time
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 group-hover:scale-110 transition-transform duration-300">
                          <Calendar className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <div className="relative flex-1">
                          <input
                            type="text"
                            id="preferredTime"
                            value={formInputs.preferredTime}
                            onChange={handleInputChange}
                            className="w-full py-3 px-3 bg-white/90 border border-gray-300/70 rounded-md text-black placeholder-transparent focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 hover:border-gray-400 peer"
                            placeholder="Select preferred time"
                            readOnly
                          />
                          <label
                            htmlFor="preferredTime"
                            className={`absolute text-sm text-black duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white/90 px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-12 ${formInputs.preferredTime ? 'top-2 scale-75 -translate-y-4' : ''}`}
                          >
                            Select preferred time
                          </label>
                        </div>
                        {formInputs.preferredTime && (
                          <button
                            type="button"
                            onClick={clearPreferredTime}
                            className="absolute right-20 top-1/2 -translate-y-1/2 text-black hover:text-gray-800 transition-colors p-1 rounded hover:bg-gray-100"
                            title="Clear Selection"
                          >
                            <X size={16} />
                          </button>
                        )}
                        <button
                          type="button"
                          onClick={() => setShowDatePicker(!showDatePicker)}
                          className={`absolute right-12 top-1/2 -translate-y-1/2 transition-all duration-300 p-1 rounded ${
                            showDatePicker
                              ? 'text-white bg-black hover:bg-gray-800'
                              : 'text-black hover:text-gray-800 hover:bg-gray-100'
                          }`}
                          title="Select Date"
                        >
                          <Calendar size={18} />
                        </button>
                        <button
                          type="button"
                          onClick={() => setShowTimePicker(!showTimePicker)}
                          className={`absolute right-3 top-1/2 -translate-y-1/2 transition-all duration-300 p-1 rounded ${
                            showTimePicker
                              ? 'text-white bg-black hover:bg-gray-800'
                              : 'text-black hover:text-gray-800 hover:bg-gray-100'
                          }`}
                          title="Select Time"
                        >
                          <Clock size={18} />
                        </button>
                      </div>

                      {/* Calendar Picker Modal */}
                      {showDatePicker && (
                        <>
                          {/* Backdrop */}
                          <div
                            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[55] animate-in fade-in duration-200"
                            onClick={() => setShowDatePicker(false)}
                          />

                          {/* Calendar Modal */}
                          <div
                            ref={datePickerRef}
                            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black border-2 border-gray-800 rounded-3xl shadow-2xl z-[60] w-80 animate-in zoom-in-95 duration-200"
                          >
                          {/* Calendar Title */}
                          <div className="relative text-center py-3 px-4 bg-black rounded-t-3xl border-b border-gray-800">
                            <h2 className="text-lg font-bold text-white flex items-center justify-center gap-2">
                              <Calendar size={20} />
                              Select Date
                            </h2>
                            <button
                              type="button"
                              onClick={() => setShowDatePicker(false)}
                              className="absolute right-3 top-1/2 -translate-y-1/2 p-1.5 hover:bg-gray-800 rounded-full transition-all duration-200 hover:scale-110"
                            >
                              <X size={16} className="text-white" />
                            </button>
                          </div>

                          {/* Calendar Header */}
                          <div className="flex items-center justify-between px-4 py-3 bg-black border-b border-gray-800">
                            <button
                              type="button"
                              onClick={() => navigateMonth('prev')}
                              className="p-2 hover:bg-gray-800 rounded-xl transition-all duration-200 hover:scale-110 group"
                            >
                              <ChevronLeft size={16} className="text-white group-hover:text-gray-300" />
                            </button>

                            <div className="flex items-center space-x-2">
                              {/* Month Dropdown */}
                              <div className="relative">
                                <select
                                  value={currentMonth.getMonth()}
                                  onChange={(e) => handleMonthChange(parseInt(e.target.value))}
                                  className="text-sm font-bold text-white bg-gray-800 border-2 border-gray-700 rounded-xl px-3 py-1.5 cursor-pointer hover:border-gray-600 focus:border-gray-500 focus:outline-none transition-all duration-200 appearance-none pr-7 hover:bg-gray-700"
                                >
                                  {getMonthNames().map((month, index) => (
                                    <option key={month} value={index} className="bg-gray-800 text-white">
                                      {month.substring(0, 3)}
                                    </option>
                                  ))}
                                </select>
                                <ChevronDown size={12} className="absolute right-2 top-1/2 -translate-y-1/2 text-white pointer-events-none" />
                              </div>

                              {/* Year Dropdown */}
                              <div className="relative">
                                <select
                                  value={currentMonth.getFullYear()}
                                  onChange={(e) => handleYearChange(parseInt(e.target.value))}
                                  className="text-sm font-bold text-white bg-gray-800 border-2 border-gray-700 rounded-xl px-3 py-1.5 cursor-pointer hover:border-gray-600 focus:border-gray-500 focus:outline-none transition-all duration-200 appearance-none pr-7 hover:bg-gray-700"
                                >
                                  {getYearRange().map((year) => (
                                    <option key={year} value={year} className="bg-gray-800 text-white">
                                      {year}
                                    </option>
                                  ))}
                                </select>
                                <ChevronDown size={12} className="absolute right-2 top-1/2 -translate-y-1/2 text-white pointer-events-none" />
                              </div>
                            </div>

                            <button
                              type="button"
                              onClick={() => navigateMonth('next')}
                              className="p-2 hover:bg-gray-800 rounded-xl transition-all duration-200 hover:scale-110 group"
                            >
                              <ChevronRight size={16} className="text-white group-hover:text-gray-300" />
                            </button>
                          </div>

                          {/* Calendar Body */}
                          <div className="px-4 pb-4 bg-black">
                            {/* Day Headers */}
                            <div className="grid grid-cols-7 gap-1 mb-2">
                              {['M', 'T', 'W', 'T', 'F', 'S', 'S'].map((day, index) => (
                                <div key={`${day}-${index}`} className="text-center text-xs font-bold text-white py-2">
                                  {day}
                                </div>
                              ))}
                            </div>

                            {/* Calendar Days */}
                            <div className="grid grid-cols-7 gap-1 mb-3">
                              {renderCalendarDays()}
                            </div>

                            {/* Today Button */}
                            <div className="flex justify-center">
                              <button
                                type="button"
                                onClick={goToToday}
                                className="px-4 py-1.5 text-xs font-bold text-black bg-white hover:bg-gray-200 rounded-full transition-all duration-200 hover:scale-105 shadow-lg"
                              >
                                📅 Today
                              </button>
                            </div>
                          </div>
                          </div>
                        </>
                      )}

                      {/* Time Picker Dropdown */}
                      {showTimePicker && (
                        <div
                          ref={timePickerRef}
                          className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto"
                        >
                          <div className="p-3 border-b border-gray-200">
                            <h4 className="font-medium text-black">Select Time</h4>
                          </div>
                          <div className="p-2 grid grid-cols-2 gap-1">
                            {generateTimeSlots().map((time) => (
                              <button
                                key={time.value}
                                type="button"
                                onClick={() => handleTimeSelect(time.value)}
                                className={`text-left px-3 py-2 rounded hover:bg-gray-100 transition-colors text-sm ${
                                  selectedTime === time.value ? 'bg-black text-white hover:bg-gray-800' : 'text-black'
                                }`}
                              >
                                {time.display}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="md:col-span-2 space-y-2">
                    <label htmlFor="problemDetails" className="block text-sm font-medium text-black">
                      Problem Details
                    </label>
                    <div className="relative group">
                      <div className="flex">
                        <div className="flex items-center justify-center w-12 h-12 group-hover:scale-110 transition-transform duration-300">
                          <MessageSquare className="h-5 w-5 text-black group-hover:text-gray-800 transition-colors duration-300" />
                        </div>
                        <textarea
                          id="problemDetails"
                          rows={4}
                          value={formInputs.problemDetails}
                          onChange={handleInputChange}
                          className="w-58 sm:w-full py-3 px-3 bg-white/90 border border-gray-300/70 rounded-md text-black placeholder-gray-400/80 focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-300 hover:border-gray-400 resize-none"
                          placeholder="Please describe the problem in detail"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Fixed Submit Button - Proper Mobile Spacing */}
                <motion.div className="w-full mt-8 sm:mt-10 px-4 pb-8 sm:pb-4">
                  <Button
                    type="submit"
                    className="w-full flex flex-row items-center justify-center 
                               px-6 py-3.5 
                               text-base 
                               font-semibold 
                               text-white 
                               bg-black 
                               hover:bg-gray-800 
                               rounded-lg 
                               transition-colors duration-200 
                               shadow-md 
                               focus:outline-none
                               focus:ring-2 
                               focus:ring-black 
                               active:scale-[0.98]
                               touch-manipulation
                               min-h-[48px]
                               mb-safe"
                  >
                    <Send className="h-5 w-5 mr-2 flex-shrink-0" />
                    <span className="whitespace-nowrap">Register Complaint</span>
                  </Button>
                </motion.div>
              </div>
            </motion.div>

            {/* Map Section - 4 columns on large screens */}
            <motion.div
              variants={itemVariants}
              className="lg:col-span-4 space-y-4 sm:space-y-6"
            >
              {/* Service Locations Map */}
              <div className="transition-all duration-700 hover:scale-[1.01] p-4 sm:p-6 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-gray-100/50 hover:border-gray-200 hover:shadow-gray-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-8 sm:-top-10 -right-8 sm:-right-10 w-16 sm:w-20 h-16 sm:h-20 bg-black/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-8 sm:-bottom-10 -left-8 sm:-left-10 w-16 sm:w-20 h-16 sm:h-20 bg-gray-900/10 rounded-full blur-xl"></div>

                {/* Header with shine effect */}
                <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-gray-50/50 to-transparent">
                  <h3 className="text-xl font-bold text-black py-3 px-2 sm:px-4 drop-shadow-sm flex items-center">
                    <MapPin className="h-5 w-5 mr-2 text-black" />
                    Service Locations
                  </h3>
                  {/* Enhanced shine effect */}
                  <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                </div>

                <div className="relative overflow-hidden rounded-lg shadow-md group">
                  <img
                    src="/background_images/Service-Locations-India.jpeg"
                    alt="Krykard service locations map of India"
                    className="w-full h-auto object-cover transition-transform duration-700 group-hover:scale-105"
                    style={{ minHeight: '180px', maxHeight: 'none' }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-4">
                    <div className="text-white">
                      <p className="font-semibold">Our nationwide presence</p>
                      <p className="text-sm text-white/90">Red dots indicate service centers</p>
                    </div>
                  </div>
                </div>

                <div className="mt-4 sm:mt-6 space-y-3 sm:space-y-4">
                  <div className="flex items-center space-x-2 bg-gray-50/80 rounded-md p-3 transition-all duration-300 hover:bg-gray-50 hover:shadow-sm">
                    <Mail className="h-5 w-5 text-black flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-black">Email Support</p>
                      <a href="mailto:<EMAIL>" className="text-sm text-gray-800 hover:text-black transition-colors">
                       <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 bg-gray-50/80 rounded-md p-3 transition-all duration-300 hover:bg-gray-50 hover:shadow-sm">
                    <Phone className="h-5 w-5 text-black flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-black">24/7 Helpline</p>
                      <a href="tel:+911234567890" className="text-sm text-gray-800 hover:text-black transition-colors">
                        +91 95000 97966
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick FAQ Section */}
              <div className="transition-all duration-700 hover:scale-[1.01] p-4 sm:p-6 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-gray-100/50 hover:border-gray-200 hover:shadow-gray-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-8 sm:-top-10 -right-8 sm:-right-10 w-16 sm:w-20 h-16 sm:h-20 bg-black/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-8 sm:-bottom-10 -left-8 sm:-left-10 w-16 sm:w-20 h-16 sm:h-20 bg-gray-900/10 rounded-full blur-xl"></div>

                {/* Header with shine effect */}
                <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-gray-50/50 to-transparent">
                  <h3 className="text-xl font-bold text-black py-3 px-2 sm:px-4 drop-shadow-sm flex items-center">
                    <MessageSquare className="h-5 w-5 mr-2 text-black" />
                    Quick FAQ
                  </h3>
                  {/* Enhanced shine effect */}
                  <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                </div>

                <div className="space-y-3 sm:space-y-4">
                  <div className="bg-gray-50/50 p-3 sm:p-4 rounded-lg transition-all duration-300 hover:bg-gray-50 hover:shadow-sm">
                    <h4 className="font-medium text-black mb-2">What happens after I submit?</h4>
                    <p className="text-sm text-gray-700">Our team will contact you within 24 hours to schedule a visit or address your query remotely.</p>
                  </div>

                  <div className="bg-gray-50/50 p-3 sm:p-4 rounded-lg transition-all duration-300 hover:bg-gray-50 hover:shadow-sm">
                    <h4 className="font-medium text-black mb-2">How can I track my complaint?</h4>
                    <p className="text-sm text-gray-700">Use the "Track Service Call" tab with your complaint ID to check the status of your service request.</p>
                  </div>

                  <div className="bg-gray-50/50 p-3 sm:p-4 rounded-lg transition-all duration-300 hover:bg-gray-50 hover:shadow-sm">
                    <h4 className="font-medium text-black mb-2">Is there an emergency support?</h4>
                    <p className="text-sm text-gray-700">Yes, mark your call as "Urgent" for priority service or call our 24/7 helpline for immediate assistance.</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Add the animations */}
        <style>{`
          @keyframes shine {
            0% {
              left: -100%;
            }
            100% {
              left: 100%;
            }
          }

          @keyframes pulse {
            0%, 100% {
              opacity: 0.2;
            }
            50% {
              opacity: 0.5;
            }
          }

          @keyframes ping {
            0% {
              transform: scale(1);
              opacity: 0.8;
            }
            75%, 100% {
              transform: scale(1.5);
              opacity: 0;
            }
          }

          .animate-shine {
            animation: shine 3s infinite;
          }

          .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
          }

          .animate-ping {
            animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
          }

          /* Gradient shift animation */
          @keyframes gradient-shift {
            0% {
              background-position: 0% 50%;
            }
            50% {
              background-position: 100% 50%;
            }
            100% {
              background-position: 0% 50%;
            }
          }

          .animate-gradient {
            background-size: 200% 200%;
            animation: gradient-shift 5s ease infinite;
          }
        `}</style>
      </div>

      {/* Back to Contact button */}
      <div className="max-w-7xl mx-auto px-2 sm:px-4 pb-6 sm:pb-8 text-center">
        <Link
          to="/contact"
          className="inline-flex items-center gap-2 text-black hover:text-gray-800 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Contact Page
        </Link>
      </div>
    </PageLayout>
  );
};

export default Service;